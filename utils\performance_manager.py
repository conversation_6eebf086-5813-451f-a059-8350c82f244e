#!/usr/bin/env python3
# -*- coding: utf-8 -*-
"""
مدير الأداء والتحسين
Performance and Optimization Manager
"""

import threading
import time
from typing import Dict, Any, Optional, List, Callable
import sqlite3
from functools import wraps
import weakref

class CacheManager:
    """مدير الذاكرة المؤقتة"""
    
    def __init__(self, max_size: int = 1000, ttl: int = 300):
        self.max_size = max_size
        self.ttl = ttl  # Time to live in seconds
        self.cache: Dict[str, Dict[str, Any]] = {}
        self.access_times: Dict[str, float] = {}
        self.lock = threading.RLock()
    
    def get(self, key: str) -> Optional[Any]:
        """الحصول على قيمة من الذاكرة المؤقتة"""
        with self.lock:
            if key not in self.cache:
                return None
            
            # التحقق من انتهاء الصلاحية
            if time.time() - self.cache[key]['timestamp'] > self.ttl:
                self._remove(key)
                return None
            
            # تحديث وقت الوصول
            self.access_times[key] = time.time()
            return self.cache[key]['value']
    
    def set(self, key: str, value: Any) -> None:
        """تعيين قيمة في الذاكرة المؤقتة"""
        with self.lock:
            # إزالة العناصر القديمة إذا تجاوز الحد الأقصى
            if len(self.cache) >= self.max_size:
                self._evict_oldest()
            
            self.cache[key] = {
                'value': value,
                'timestamp': time.time()
            }
            self.access_times[key] = time.time()
    
    def remove(self, key: str) -> None:
        """إزالة قيمة من الذاكرة المؤقتة"""
        with self.lock:
            self._remove(key)
    
    def clear(self) -> None:
        """مسح جميع القيم"""
        with self.lock:
            self.cache.clear()
            self.access_times.clear()
    
    def _remove(self, key: str) -> None:
        """إزالة قيمة (دالة داخلية)"""
        self.cache.pop(key, None)
        self.access_times.pop(key, None)
    
    def _evict_oldest(self) -> None:
        """إزالة أقدم العناصر"""
        if not self.access_times:
            return
        
        # العثور على أقدم عنصر
        oldest_key = min(self.access_times.keys(), key=lambda k: self.access_times[k])
        self._remove(oldest_key)

class ConnectionPool:
    """مجموعة اتصالات قاعدة البيانات"""
    
    def __init__(self, database_path: str, max_connections: int = 10):
        self.database_path = database_path
        self.max_connections = max_connections
        self.connections: List[sqlite3.Connection] = []
        self.available_connections: List[sqlite3.Connection] = []
        self.lock = threading.RLock()
        
        # إنشاء الاتصالات الأولية
        self._create_initial_connections()
    
    def _create_initial_connections(self):
        """إنشاء الاتصالات الأولية"""
        for _ in range(min(3, self.max_connections)):
            conn = self._create_connection()
            if conn:
                self.connections.append(conn)
                self.available_connections.append(conn)
    
    def _create_connection(self) -> Optional[sqlite3.Connection]:
        """إنشاء اتصال جديد"""
        try:
            conn = sqlite3.connect(
                self.database_path,
                check_same_thread=False,
                timeout=30.0
            )
            conn.row_factory = sqlite3.Row
            
            # تحسينات الأداء
            conn.execute("PRAGMA journal_mode=WAL")
            conn.execute("PRAGMA synchronous=NORMAL")
            conn.execute("PRAGMA cache_size=10000")
            conn.execute("PRAGMA temp_store=MEMORY")
            
            return conn
        except Exception:
            return None
    
    def get_connection(self) -> Optional[sqlite3.Connection]:
        """الحصول على اتصال من المجموعة"""
        with self.lock:
            if self.available_connections:
                return self.available_connections.pop()
            
            # إنشاء اتصال جديد إذا لم نصل للحد الأقصى
            if len(self.connections) < self.max_connections:
                conn = self._create_connection()
                if conn:
                    self.connections.append(conn)
                    return conn
            
            return None
    
    def return_connection(self, conn: sqlite3.Connection):
        """إرجاع اتصال للمجموعة"""
        with self.lock:
            if conn in self.connections:
                self.available_connections.append(conn)
    
    def close_all(self):
        """إغلاق جميع الاتصالات"""
        with self.lock:
            for conn in self.connections:
                try:
                    conn.close()
                except:
                    pass
            self.connections.clear()
            self.available_connections.clear()

class PerformanceManager:
    """مدير الأداء الرئيسي"""
    
    def __init__(self):
        self.cache = CacheManager()
        self.connection_pool: Optional[ConnectionPool] = None
        self.query_stats: Dict[str, Dict[str, Any]] = {}
        self.lock = threading.RLock()
        
        # إحصائيات الأداء
        self.stats = {
            'cache_hits': 0,
            'cache_misses': 0,
            'total_queries': 0,
            'avg_query_time': 0.0
        }
    
    def initialize_db_pool(self, database_path: str, max_connections: int = 10):
        """تهيئة مجموعة اتصالات قاعدة البيانات"""
        self.connection_pool = ConnectionPool(database_path, max_connections)
    
    def get_db_connection(self) -> Optional[sqlite3.Connection]:
        """الحصول على اتصال قاعدة بيانات"""
        if self.connection_pool:
            return self.connection_pool.get_connection()
        return None
    
    def return_db_connection(self, conn: sqlite3.Connection):
        """إرجاع اتصال قاعدة البيانات"""
        if self.connection_pool:
            self.connection_pool.return_connection(conn)
    
    def cached_query(self, query: str, params: tuple = ()) -> Optional[List[Dict[str, Any]]]:
        """تنفيذ استعلام مع التخزين المؤقت"""
        # إنشاء مفتاح الذاكرة المؤقتة
        cache_key = f"{query}:{str(params)}"
        
        # محاولة الحصول على النتيجة من الذاكرة المؤقتة
        cached_result = self.cache.get(cache_key)
        if cached_result is not None:
            with self.lock:
                self.stats['cache_hits'] += 1
            return cached_result
        
        # تنفيذ الاستعلام
        start_time = time.time()
        conn = self.get_db_connection()
        
        if not conn:
            return None
        
        try:
            cursor = conn.execute(query, params)
            result = [dict(row) for row in cursor.fetchall()]
            
            # حفظ النتيجة في الذاكرة المؤقتة
            self.cache.set(cache_key, result)
            
            # تحديث الإحصائيات
            query_time = time.time() - start_time
            with self.lock:
                self.stats['cache_misses'] += 1
                self.stats['total_queries'] += 1
                
                # تحديث متوسط وقت الاستعلام
                total_time = self.stats['avg_query_time'] * (self.stats['total_queries'] - 1)
                self.stats['avg_query_time'] = (total_time + query_time) / self.stats['total_queries']
                
                # حفظ إحصائيات الاستعلام
                if query not in self.query_stats:
                    self.query_stats[query] = {
                        'count': 0,
                        'total_time': 0.0,
                        'avg_time': 0.0
                    }
                
                self.query_stats[query]['count'] += 1
                self.query_stats[query]['total_time'] += query_time
                self.query_stats[query]['avg_time'] = (
                    self.query_stats[query]['total_time'] / self.query_stats[query]['count']
                )
            
            return result
            
        except Exception as e:
            return None
        finally:
            self.return_db_connection(conn)
    
    def execute_query(self, query: str, params: tuple = ()) -> bool:
        """تنفيذ استعلام بدون إرجاع نتائج (INSERT, UPDATE, DELETE)"""
        start_time = time.time()
        conn = self.get_db_connection()
        
        if not conn:
            return False
        
        try:
            conn.execute(query, params)
            conn.commit()
            
            # مسح الذاكرة المؤقتة المتعلقة
            self._invalidate_cache_for_query(query)
            
            return True
            
        except Exception:
            return False
        finally:
            self.return_db_connection(conn)
    
    def _invalidate_cache_for_query(self, query: str):
        """إبطال الذاكرة المؤقتة للاستعلامات المتعلقة"""
        # تحديد الجداول المتأثرة
        query_lower = query.lower()
        affected_tables = []
        
        if 'products' in query_lower:
            affected_tables.append('products')
        if 'orders' in query_lower:
            affected_tables.append('orders')
        if 'users' in query_lower:
            affected_tables.append('users')
        
        # مسح الذاكرة المؤقتة للجداول المتأثرة
        keys_to_remove = []
        for key in self.cache.cache.keys():
            for table in affected_tables:
                if table in key.lower():
                    keys_to_remove.append(key)
                    break
        
        for key in keys_to_remove:
            self.cache.remove(key)
    
    def get_performance_stats(self) -> Dict[str, Any]:
        """الحصول على إحصائيات الأداء"""
        with self.lock:
            cache_hit_rate = 0.0
            total_cache_requests = self.stats['cache_hits'] + self.stats['cache_misses']
            
            if total_cache_requests > 0:
                cache_hit_rate = (self.stats['cache_hits'] / total_cache_requests) * 100
            
            return {
                'cache_hit_rate': cache_hit_rate,
                'total_queries': self.stats['total_queries'],
                'avg_query_time': self.stats['avg_query_time'],
                'cache_size': len(self.cache.cache),
                'connection_pool_size': len(self.connection_pool.connections) if self.connection_pool else 0,
                'available_connections': len(self.connection_pool.available_connections) if self.connection_pool else 0
            }
    
    def cleanup(self):
        """تنظيف الموارد"""
        self.cache.clear()
        if self.connection_pool:
            self.connection_pool.close_all()

# إنشاء مثيل عام
performance_manager = PerformanceManager()

def cached(ttl: int = 300):
    """ديكوريتر للتخزين المؤقت للدوال"""
    def decorator(func: Callable) -> Callable:
        @wraps(func)
        def wrapper(*args, **kwargs):
            # إنشاء مفتاح الذاكرة المؤقتة
            cache_key = f"{func.__name__}:{str(args)}:{str(kwargs)}"
            
            # محاولة الحصول على النتيجة من الذاكرة المؤقتة
            cached_result = performance_manager.cache.get(cache_key)
            if cached_result is not None:
                return cached_result
            
            # تنفيذ الدالة
            result = func(*args, **kwargs)
            
            # حفظ النتيجة في الذاكرة المؤقتة
            performance_manager.cache.set(cache_key, result)
            
            return result
        return wrapper
    return decorator
