#!/usr/bin/env python3
# -*- coding: utf-8 -*-
"""
نافذة إحصائيات الأداء
Performance Statistics Window
"""

import tkinter as tk
from tkinter import ttk
from typing import Dict, Any
from .theme_manager import theme_manager
from .modern_components import ModernCard, ModernButton
import threading
import time

class PerformanceWindow:
    """نافذة إحصائيات الأداء"""
    
    def __init__(self, parent: tk.Tk, app):
        self.parent = parent
        self.app = app
        self.window = None
        self.is_running = False
        self.update_thread = None
        
    def show(self):
        """عرض نافذة الإحصائيات"""
        if self.window and self.window.winfo_exists():
            self.window.lift()
            return
        
        self.window = tk.Toplevel(self.parent)
        self.window.title("📊 إحصائيات الأداء")
        self.window.geometry("800x600")
        self.window.resizable(True, True)
        
        # توسيط النافذة
        self.window.transient(self.parent)
        
        # إنشاء الواجهة
        self._create_widgets()
        
        # تطبيق الثيم
        self._apply_theme()
        
        # بدء التحديث التلقائي
        self.is_running = True
        self._start_auto_update()
        
        # معالج إغلاق النافذة
        self.window.protocol("WM_DELETE_WINDOW", self._on_closing)
    
    def _create_widgets(self):
        """إنشاء عناصر الواجهة"""
        # الشريط العلوي
        self._create_header()
        
        # المحتوى الرئيسي
        self._create_main_content()
        
        # الشريط السفلي
        self._create_footer()
    
    def _create_header(self):
        """إنشاء الشريط العلوي"""
        self.header_frame = tk.Frame(self.window, height=60)
        self.header_frame.pack(fill='x', side='top')
        self.header_frame.pack_propagate(False)
        
        # العنوان
        self.title_label = tk.Label(
            self.header_frame,
            text="📊 إحصائيات الأداء والتحسين",
            font=('Segoe UI', 16, 'bold')
        )
        self.title_label.pack(pady=15)
        
        # خط فاصل
        separator = ttk.Separator(self.window, orient='horizontal')
        separator.pack(fill='x', padx=10)
    
    def _create_main_content(self):
        """إنشاء المحتوى الرئيسي"""
        self.main_frame = tk.Frame(self.window)
        self.main_frame.pack(fill='both', expand=True, padx=10, pady=10)
        
        # الجانب الأيسر - إحصائيات الذاكرة المؤقتة
        self.cache_frame = tk.Frame(self.main_frame, width=380)
        self.cache_frame.pack(side='left', fill='y', padx=(0, 5))
        self.cache_frame.pack_propagate(False)
        
        # بطاقة الذاكرة المؤقتة
        self.cache_card = ModernCard(self.cache_frame, title="🗄️ إحصائيات الذاكرة المؤقتة")
        self.cache_card.pack(fill='both', expand=True)
        
        # محتوى الذاكرة المؤقتة
        self.cache_content_frame = tk.Frame(self.cache_card.content_frame)
        self.cache_content_frame.pack(fill='both', expand=True, padx=15, pady=15)
        
        # الجانب الأيمن - إحصائيات قاعدة البيانات
        self.db_frame = tk.Frame(self.main_frame)
        self.db_frame.pack(side='right', fill='both', expand=True, padx=(5, 0))
        
        # بطاقة قاعدة البيانات
        self.db_card = ModernCard(self.db_frame, title="🗃️ إحصائيات قاعدة البيانات")
        self.db_card.pack(fill='both', expand=True)
        
        # محتوى قاعدة البيانات
        self.db_content_frame = tk.Frame(self.db_card.content_frame)
        self.db_content_frame.pack(fill='both', expand=True, padx=15, pady=15)
        
        # تحديث الإحصائيات
        self._update_stats()
    
    def _create_footer(self):
        """إنشاء الشريط السفلي"""
        # خط فاصل
        separator = ttk.Separator(self.window, orient='horizontal')
        separator.pack(fill='x', padx=10)
        
        self.footer_frame = tk.Frame(self.window, height=50)
        self.footer_frame.pack(fill='x', side='bottom')
        self.footer_frame.pack_propagate(False)
        
        # أزرار التحكم
        self.controls_frame = tk.Frame(self.footer_frame)
        self.controls_frame.pack(pady=10)
        
        # زر التحديث اليدوي
        self.refresh_button = ModernButton(
            self.controls_frame,
            text="🔄 تحديث",
            command=self._manual_refresh,
            button_type="primary"
        )
        self.refresh_button.pack(side='left', padx=5)
        
        # زر مسح الذاكرة المؤقتة
        self.clear_cache_button = ModernButton(
            self.controls_frame,
            text="🗑️ مسح الذاكرة المؤقتة",
            command=self._clear_cache,
            button_type="warning"
        )
        self.clear_cache_button.pack(side='left', padx=5)
        
        # زر إغلاق
        self.close_button = ModernButton(
            self.controls_frame,
            text="❌ إغلاق",
            command=self._on_closing,
            button_type="danger"
        )
        self.close_button.pack(side='left', padx=5)
        
        # حالة التحديث التلقائي
        self.auto_update_label = tk.Label(
            self.footer_frame,
            text="🔄 التحديث التلقائي: مفعل",
            font=('Segoe UI', 9)
        )
        self.auto_update_label.pack(side='right', padx=10, pady=15)
    
    def _update_stats(self):
        """تحديث الإحصائيات"""
        try:
            # الحصول على إحصائيات الأداء
            stats = self.app.db_manager.get_performance_stats()
            
            # تحديث إحصائيات الذاكرة المؤقتة
            self._update_cache_stats(stats)
            
            # تحديث إحصائيات قاعدة البيانات
            self._update_db_stats(stats)
            
        except Exception as e:
            print(f"خطأ في تحديث الإحصائيات: {e}")
    
    def _update_cache_stats(self, stats: Dict[str, Any]):
        """تحديث إحصائيات الذاكرة المؤقتة"""
        # مسح المحتوى الحالي
        for widget in self.cache_content_frame.winfo_children():
            widget.destroy()
        
        # إحصائيات الذاكرة المؤقتة
        cache_data = [
            ("📈 معدل النجاح", f"{stats.get('cache_hit_rate', 0):.1f}%"),
            ("📦 حجم الذاكرة المؤقتة", str(stats.get('cache_size', 0))),
            ("⚡ الاستعلامات المحفوظة", "متاح"),
            ("🔄 التحديث التلقائي", "كل 5 ثوانٍ")
        ]
        
        for i, (label, value) in enumerate(cache_data):
            self._create_stat_row(self.cache_content_frame, label, value, i)
        
        # شريط تقدم معدل النجاح
        progress_frame = tk.Frame(self.cache_content_frame)
        progress_frame.pack(fill='x', pady=10)
        
        progress_label = tk.Label(
            progress_frame,
            text="معدل نجاح الذاكرة المؤقتة:",
            font=('Segoe UI', 9)
        )
        progress_label.pack(anchor='w')
        
        progress_bar = ttk.Progressbar(
            progress_frame,
            length=300,
            mode='determinate'
        )
        progress_bar.pack(fill='x', pady=5)
        progress_bar['value'] = stats.get('cache_hit_rate', 0)
    
    def _update_db_stats(self, stats: Dict[str, Any]):
        """تحديث إحصائيات قاعدة البيانات"""
        # مسح المحتوى الحالي
        for widget in self.db_content_frame.winfo_children():
            widget.destroy()
        
        # إحصائيات قاعدة البيانات
        db_data = [
            ("📊 إجمالي الاستعلامات", str(stats.get('total_queries', 0))),
            ("⏱️ متوسط وقت الاستعلام", f"{stats.get('avg_query_time', 0):.3f} ثانية"),
            ("🔗 مجموعة الاتصالات", str(stats.get('connection_pool_size', 0))),
            ("✅ الاتصالات المتاحة", str(stats.get('available_connections', 0)))
        ]
        
        for i, (label, value) in enumerate(db_data):
            self._create_stat_row(self.db_content_frame, label, value, i)
        
        # مؤشر الأداء
        performance_frame = tk.Frame(self.db_content_frame)
        performance_frame.pack(fill='x', pady=10)
        
        performance_label = tk.Label(
            performance_frame,
            text="مؤشر الأداء العام:",
            font=('Segoe UI', 9)
        )
        performance_label.pack(anchor='w')
        
        # حساب مؤشر الأداء
        cache_score = min(stats.get('cache_hit_rate', 0), 100)
        speed_score = max(0, 100 - (stats.get('avg_query_time', 0) * 1000))
        overall_score = (cache_score + speed_score) / 2
        
        performance_bar = ttk.Progressbar(
            performance_frame,
            length=300,
            mode='determinate'
        )
        performance_bar.pack(fill='x', pady=5)
        performance_bar['value'] = overall_score
        
        # تصنيف الأداء
        if overall_score >= 80:
            performance_text = "ممتاز 🟢"
        elif overall_score >= 60:
            performance_text = "جيد 🟡"
        else:
            performance_text = "يحتاج تحسين 🔴"
        
        performance_status = tk.Label(
            performance_frame,
            text=f"التقييم: {performance_text} ({overall_score:.1f}%)",
            font=('Segoe UI', 9, 'bold')
        )
        performance_status.pack(anchor='w', pady=5)
    
    def _create_stat_row(self, parent: tk.Widget, label: str, value: str, row: int):
        """إنشاء صف إحصائية"""
        row_frame = tk.Frame(parent)
        row_frame.pack(fill='x', pady=2)
        
        label_widget = tk.Label(
            row_frame,
            text=label,
            font=('Segoe UI', 10),
            anchor='w'
        )
        label_widget.pack(side='left')
        
        value_widget = tk.Label(
            row_frame,
            text=value,
            font=('Segoe UI', 10, 'bold'),
            anchor='e'
        )
        value_widget.pack(side='right')
        
        # تطبيق الثيم
        colors = theme_manager.get_current_theme()['colors']
        row_frame.configure(bg=colors['card'])
        label_widget.configure(bg=colors['card'], fg=colors['text_secondary'])
        value_widget.configure(bg=colors['card'], fg=colors['text_primary'])
    
    def _apply_theme(self):
        """تطبيق الثيم"""
        colors = theme_manager.get_current_theme()['colors']
        
        # النافذة الرئيسية
        self.window.configure(bg=colors['background'])
        
        # الشريط العلوي
        self.header_frame.configure(bg=colors['surface'])
        self.title_label.configure(bg=colors['surface'], fg=colors['primary'])
        
        # المحتوى الرئيسي
        self.main_frame.configure(bg=colors['background'])
        self.cache_frame.configure(bg=colors['background'])
        self.db_frame.configure(bg=colors['background'])
        
        self.cache_content_frame.configure(bg=colors['card'])
        self.db_content_frame.configure(bg=colors['card'])
        
        # الشريط السفلي
        self.footer_frame.configure(bg=colors['surface'])
        self.controls_frame.configure(bg=colors['surface'])
        self.auto_update_label.configure(bg=colors['surface'], fg=colors['text_hint'])
    
    def _start_auto_update(self):
        """بدء التحديث التلقائي"""
        def update_loop():
            while self.is_running:
                if self.window and self.window.winfo_exists():
                    try:
                        self.window.after(0, self._update_stats)
                    except:
                        break
                time.sleep(5)  # تحديث كل 5 ثوانٍ
        
        self.update_thread = threading.Thread(target=update_loop, daemon=True)
        self.update_thread.start()
    
    def _manual_refresh(self):
        """التحديث اليدوي"""
        self._update_stats()
    
    def _clear_cache(self):
        """مسح الذاكرة المؤقتة"""
        try:
            from utils.performance_manager import performance_manager
            performance_manager.cache.clear()
            self._update_stats()
        except Exception as e:
            print(f"خطأ في مسح الذاكرة المؤقتة: {e}")
    
    def _on_closing(self):
        """معالج إغلاق النافذة"""
        self.is_running = False
        if self.window:
            self.window.destroy()
            self.window = None
