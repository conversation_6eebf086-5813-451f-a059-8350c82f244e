@echo off
echo ======================================
echo  نظام نقطة البيع - شاورما الدجاج
echo  Shawarma Chicken POS System
echo ======================================
echo.

REM تحديد مجلد العمل
cd /d "%~dp0"

REM التحقق من وجود Python
python --version >nul 2>&1
if errorlevel 1 (
    echo خطأ: Python غير مثبت أو غير موجود في PATH
    echo يرجى تثبيت Python 3.8 أو أحدث
    pause
    exit /b 1
)

REM عرض معلومات Python
echo تم العثور على Python:
python --version
echo.

REM التحقق من وجود الملف الرئيسي
if not exist "main.py" (
    echo خطأ: ملف main.py غير موجود
    echo تأكد من أنك في المجلد الصحيح
    pause
    exit /b 1
)

REM تشغيل النظام
echo بدء تشغيل نظام نقطة البيع...
echo.
python main.py

REM في حالة حدوث خطأ
if errorlevel 1 (
    echo.
    echo حدث خطأ أثناء تشغيل النظام
    echo يرجى مراجعة الأخطاء أعلاه
    pause
)

echo.
echo تم إغلاق النظام
pause