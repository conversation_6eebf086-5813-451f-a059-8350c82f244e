#!/usr/bin/env python3
# -*- coding: utf-8 -*-
"""
مدير الثيمات والألوان
Theme and Color Manager
"""

import tkinter as tk
from tkinter import ttk
from typing import Dict, Any, Optional
import json
import os

class ThemeManager:
    """مدير الثيمات والألوان"""
    
    def __init__(self):
        self.current_theme = "light"
        self.themes = self._load_themes()
        self.config_file = "theme_config.json"
        self._load_config()
    
    def _load_themes(self) -> Dict[str, Dict[str, Any]]:
        """تحميل الثيمات المتاحة"""
        return {
            "light": {
                "name": "فاتح",
                "name_en": "Light",
                "colors": {
                    # الألوان الأساسية
                    "primary": "#2196F3",
                    "primary_dark": "#1976D2", 
                    "primary_light": "#BBDEFB",
                    "secondary": "#FF9800",
                    "secondary_dark": "#F57C00",
                    "secondary_light": "#FFE0B2",
                    
                    # ألوان الخلفية
                    "background": "#FFFFFF",
                    "surface": "#F5F5F5",
                    "card": "#FFFFFF",
                    "sidebar": "#FAFAFA",
                    
                    # ألوان النص
                    "text_primary": "#212121",
                    "text_secondary": "#757575",
                    "text_hint": "#BDBDBD",
                    "text_on_primary": "#FFFFFF",
                    "text_on_secondary": "#FFFFFF",
                    
                    # ألوان الحالة
                    "success": "#4CAF50",
                    "warning": "#FF9800", 
                    "error": "#F44336",
                    "info": "#2196F3",
                    
                    # ألوان الحدود والظلال
                    "border": "#E0E0E0",
                    "shadow": "#E0E0E0",
                    "hover": "#F5F5F5",
                    "selected": "#E3F2FD",
                    
                    # ألوان الأزرار
                    "button_primary": "#2196F3",
                    "button_secondary": "#757575",
                    "button_success": "#4CAF50",
                    "button_danger": "#F44336",
                    "button_warning": "#FF9800",
                }
            },
            
            "dark": {
                "name": "داكن",
                "name_en": "Dark",
                "colors": {
                    # الألوان الأساسية
                    "primary": "#64B5F6",
                    "primary_dark": "#42A5F5",
                    "primary_light": "#90CAF9",
                    "secondary": "#FFB74D",
                    "secondary_dark": "#FFA726",
                    "secondary_light": "#FFCC80",
                    
                    # ألوان الخلفية
                    "background": "#121212",
                    "surface": "#1E1E1E",
                    "card": "#2D2D2D",
                    "sidebar": "#1A1A1A",
                    
                    # ألوان النص
                    "text_primary": "#FFFFFF",
                    "text_secondary": "#B3B3B3",
                    "text_hint": "#666666",
                    "text_on_primary": "#000000",
                    "text_on_secondary": "#000000",
                    
                    # ألوان الحالة
                    "success": "#66BB6A",
                    "warning": "#FFB74D",
                    "error": "#EF5350",
                    "info": "#64B5F6",
                    
                    # ألوان الحدود والظلال
                    "border": "#404040",
                    "shadow": "#404040",
                    "hover": "#2D2D2D",
                    "selected": "#1A237E",
                    
                    # ألوان الأزرار
                    "button_primary": "#64B5F6",
                    "button_secondary": "#757575",
                    "button_success": "#66BB6A",
                    "button_danger": "#EF5350",
                    "button_warning": "#FFB74D",
                }
            },
            
            "colorful": {
                "name": "ملون",
                "name_en": "Colorful",
                "colors": {
                    # الألوان الأساسية
                    "primary": "#E91E63",
                    "primary_dark": "#C2185B",
                    "primary_light": "#F8BBD9",
                    "secondary": "#9C27B0",
                    "secondary_dark": "#7B1FA2",
                    "secondary_light": "#E1BEE7",
                    
                    # ألوان الخلفية
                    "background": "#FAFAFA",
                    "surface": "#FFFFFF",
                    "card": "#FFFFFF",
                    "sidebar": "#F3E5F5",
                    
                    # ألوان النص
                    "text_primary": "#2E2E2E",
                    "text_secondary": "#616161",
                    "text_hint": "#9E9E9E",
                    "text_on_primary": "#FFFFFF",
                    "text_on_secondary": "#FFFFFF",
                    
                    # ألوان الحالة
                    "success": "#00E676",
                    "warning": "#FF6D00",
                    "error": "#FF1744",
                    "info": "#00B0FF",
                    
                    # ألوان الحدود والظلال
                    "border": "#E0E0E0",
                    "shadow": "#E0E0E0",
                    "hover": "#FCE4EC",
                    "selected": "#F8BBD9",
                    
                    # ألوان الأزرار
                    "button_primary": "#E91E63",
                    "button_secondary": "#9C27B0",
                    "button_success": "#00E676",
                    "button_danger": "#FF1744",
                    "button_warning": "#FF6D00",
                }
            }
        }
    
    def _load_config(self):
        """تحميل إعدادات الثيم"""
        try:
            if os.path.exists(self.config_file):
                with open(self.config_file, 'r', encoding='utf-8') as f:
                    config = json.load(f)
                    self.current_theme = config.get('theme', 'light')
        except:
            self.current_theme = 'light'
    
    def _save_config(self):
        """حفظ إعدادات الثيم"""
        try:
            config = {'theme': self.current_theme}
            with open(self.config_file, 'w', encoding='utf-8') as f:
                json.dump(config, f, ensure_ascii=False, indent=2)
        except:
            pass
    
    def get_current_theme(self) -> Dict[str, Any]:
        """الحصول على الثيم الحالي"""
        return self.themes.get(self.current_theme, self.themes['light'])
    
    def get_color(self, color_name: str) -> str:
        """الحصول على لون معين من الثيم الحالي"""
        theme = self.get_current_theme()
        return theme['colors'].get(color_name, '#000000')
    
    def set_theme(self, theme_name: str):
        """تعيين ثيم جديد"""
        if theme_name in self.themes:
            self.current_theme = theme_name
            self._save_config()
    
    def get_available_themes(self) -> Dict[str, str]:
        """الحصول على قائمة الثيمات المتاحة"""
        return {name: theme['name'] for name, theme in self.themes.items()}
    
    def apply_theme_to_widget(self, widget: tk.Widget, widget_type: str = "default"):
        """تطبيق الثيم على عنصر واجهة"""
        theme = self.get_current_theme()
        colors = theme['colors']
        
        try:
            if isinstance(widget, tk.Tk) or isinstance(widget, tk.Toplevel):
                widget.configure(bg=colors['background'])
            
            elif isinstance(widget, tk.Frame):
                widget.configure(bg=colors['surface'])
            
            elif isinstance(widget, tk.Label):
                widget.configure(
                    bg=colors['surface'],
                    fg=colors['text_primary'],
                    font=('Segoe UI', 10)
                )
            
            elif isinstance(widget, tk.Button):
                if widget_type == "primary":
                    widget.configure(
                        bg=colors['button_primary'],
                        fg=colors['text_on_primary'],
                        activebackground=colors['primary_dark'],
                        activeforeground=colors['text_on_primary'],
                        relief='flat',
                        font=('Segoe UI', 10, 'bold'),
                        cursor='hand2'
                    )
                elif widget_type == "success":
                    widget.configure(
                        bg=colors['button_success'],
                        fg=colors['text_on_primary'],
                        activebackground=colors['success'],
                        activeforeground=colors['text_on_primary'],
                        relief='flat',
                        font=('Segoe UI', 10, 'bold'),
                        cursor='hand2'
                    )
                elif widget_type == "danger":
                    widget.configure(
                        bg=colors['button_danger'],
                        fg=colors['text_on_primary'],
                        activebackground=colors['error'],
                        activeforeground=colors['text_on_primary'],
                        relief='flat',
                        font=('Segoe UI', 10, 'bold'),
                        cursor='hand2'
                    )
                else:
                    widget.configure(
                        bg=colors['surface'],
                        fg=colors['text_primary'],
                        activebackground=colors['hover'],
                        activeforeground=colors['text_primary'],
                        relief='flat',
                        font=('Segoe UI', 10),
                        cursor='hand2'
                    )
            
            elif isinstance(widget, tk.Entry):
                widget.configure(
                    bg=colors['card'],
                    fg=colors['text_primary'],
                    insertbackground=colors['text_primary'],
                    relief='flat',
                    font=('Segoe UI', 10)
                )
        
        except Exception as e:
            pass  # تجاهل الأخطاء في التطبيق

# إنشاء مثيل عام للمدير
theme_manager = ThemeManager()
