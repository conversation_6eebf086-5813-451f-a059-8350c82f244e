#!/usr/bin/env python3
# -*- coding: utf-8 -*-
"""
نافذة إدارة المخزون - نظام نقطة البيع
Inventory Management Dialog for POS System
"""

import tkinter as tk
from tkinter import ttk, messagebox
from datetime import datetime

class InventoryDialog:
    """نافذة إدارة المخزون"""
    
    def __init__(self, parent, db_manager, config):
        """تهيئة نافذة إدارة المخزون"""
        self.parent = parent
        self.db_manager = db_manager
        self.config = config
        
        self.dialog = None
        self.inventory_tree = None
        
        # بيانات المخزون التجريبية
        self.sample_inventory = [
            {'id': 1, 'name': 'لحم الشاورما', 'current_stock': 5.5, 'minimum_stock': 10.0, 'unit': 'كيلو', 'cost_price': 45.00, 'status': 'مخزون منخفض'},
            {'id': 2, 'name': 'خبز الشاورما', 'current_stock': 150, 'minimum_stock': 50, 'unit': 'رغيف', 'cost_price': 0.5, 'status': 'متوفر'},
            {'id': 3, 'name': 'دجاج مشوي', 'current_stock': 25, 'minimum_stock': 15, 'unit': 'قطعة', 'cost_price': 12.00, 'status': 'متوفر'},
            {'id': 4, 'name': 'خضار مشكلة', 'current_stock': 0, 'minimum_stock': 5, 'unit': 'كيلو', 'cost_price': 8.00, 'status': 'نفد المخزون'},
            {'id': 5, 'name': 'صلصة الثوم', 'current_stock': 8, 'minimum_stock': 10, 'unit': 'لتر', 'cost_price': 15.00, 'status': 'مخزون منخفض'},
        ]
    
    def show(self):
        """عرض نافذة إدارة المخزون"""
        self.dialog = tk.Toplevel(self.parent)
        self.dialog.title("إدارة المخزون")
        self.dialog.geometry("900x600")
        self.dialog.resizable(True, True)
        
        # توسيط النافذة
        self._center_dialog()
        
        # جعل النافذة مودالية
        self.dialog.transient(self.parent)
        self.dialog.grab_set()
        
        # إنشاء الواجهة
        self._create_widgets()
        
        # تحميل البيانات
        self._load_inventory_data()
    
    def _center_dialog(self):
        """توسيط النافذة"""
        self.dialog.update_idletasks()
        
        parent_x = self.parent.winfo_rootx()
        parent_y = self.parent.winfo_rooty()
        parent_width = self.parent.winfo_width()
        parent_height = self.parent.winfo_height()
        
        x = parent_x + (parent_width - 900) // 2
        y = parent_y + (parent_height - 600) // 2
        
        self.dialog.geometry(f"900x600+{x}+{y}")
    
    def _create_widgets(self):
        """إنشاء عناصر الواجهة"""
        # الإطار الرئيسي
        main_frame = ttk.Frame(self.dialog, padding="10")
        main_frame.pack(fill='both', expand=True)
        
        # العنوان
        title_label = ttk.Label(
            main_frame,
            text="📦 إدارة المخزون",
            font=("Tahoma", 16, "bold"),
            foreground="#2C3E50"
        )
        title_label.pack(pady=(0, 20))
        
        # شريط الأدوات
        self._create_toolbar(main_frame)
        
        # جدول المخزون
        self._create_inventory_table(main_frame)
        
        # شريط المعلومات
        self._create_info_bar(main_frame)
        
        # أزرار التحكم
        self._create_control_buttons(main_frame)
    
    def _create_toolbar(self, parent):
        """إنشاء شريط الأدوات"""
        toolbar_frame = ttk.Frame(parent)
        toolbar_frame.pack(fill='x', pady=(0, 15))
        
        # أزرار الإجراءات
        actions_frame = ttk.Frame(toolbar_frame)
        actions_frame.pack(side='left')
        
        ttk.Button(
            actions_frame,
            text="+ إضافة صنف",
            command=self._add_inventory_item
        ).pack(side='left', padx=2)
        
        ttk.Button(
            actions_frame,
            text="✏️ تعديل",
            command=self._edit_inventory_item
        ).pack(side='left', padx=2)
        
        ttk.Button(
            actions_frame,
            text="📈 تحديث الكمية",
            command=self._update_stock
        ).pack(side='left', padx=2)
        
        # البحث
        search_frame = ttk.Frame(toolbar_frame)
        search_frame.pack(side='right', padx=5)
        
        ttk.Label(search_frame, text="البحث:", font=("Tahoma", 10, "bold")).pack(side='right', padx=(0, 5))
        
        self.search_var = tk.StringVar()
        search_entry = ttk.Entry(
            search_frame,
            textvariable=self.search_var,
            width=25,
            font=("Tahoma", 10)
        )
        search_entry.pack(side='right', padx=5)
        search_entry.bind('<KeyRelease>', self._on_search)
    
    def _create_inventory_table(self, parent):
        """إنشاء جدول المخزون"""
        table_frame = ttk.LabelFrame(parent, text="عناصر المخزون", padding="10")
        table_frame.pack(fill='both', expand=True, pady=(0, 15))
        
        # أعمدة الجدول
        columns = ('id', 'name', 'current_stock', 'minimum_stock', 'unit', 'cost_price', 'total_value', 'status')
        
        self.inventory_tree = ttk.Treeview(table_frame, columns=columns, show='headings', height=15)
        
        # تحديد عناوين الأعمدة
        self.inventory_tree.heading('id', text='الرقم', anchor='center')
        self.inventory_tree.heading('name', text='اسم الصنف', anchor='center')
        self.inventory_tree.heading('current_stock', text='الكمية الحالية', anchor='center')
        self.inventory_tree.heading('minimum_stock', text='الحد الأدنى', anchor='center')
        self.inventory_tree.heading('unit', text='الوحدة', anchor='center')
        self.inventory_tree.heading('cost_price', text='سعر التكلفة', anchor='center')
        self.inventory_tree.heading('total_value', text='القيمة الإجمالية', anchor='center')
        self.inventory_tree.heading('status', text='الحالة', anchor='center')
        
        # تحديد عرض الأعمدة
        self.inventory_tree.column('id', width=60, anchor='center')
        self.inventory_tree.column('name', width=200, anchor='center')
        self.inventory_tree.column('current_stock', width=100, anchor='center')
        self.inventory_tree.column('minimum_stock', width=100, anchor='center')
        self.inventory_tree.column('unit', width=80, anchor='center')
        self.inventory_tree.column('cost_price', width=100, anchor='center')
        self.inventory_tree.column('total_value', width=120, anchor='center')
        self.inventory_tree.column('status', width=100, anchor='center')
        
        # شريط التمرير
        scrollbar = ttk.Scrollbar(table_frame, orient='vertical', command=self.inventory_tree.yview)
        self.inventory_tree.configure(yscrollcommand=scrollbar.set)
        
        # تخطيط الجدول
        self.inventory_tree.pack(side='left', fill='both', expand=True)
        scrollbar.pack(side='right', fill='y')
        
        # ربط الأحداث
        self.inventory_tree.bind('<Double-1>', self._on_item_double_click)
    
    def _create_info_bar(self, parent):
        """إنشاء شريط المعلومات"""
        info_frame = ttk.Frame(parent)
        info_frame.pack(fill='x', pady=(0, 15))
        
        # إحصائيات المخزون
        stats_frame = ttk.LabelFrame(info_frame, text="إحصائيات المخزون", padding="10")
        stats_frame.pack(fill='x')
        
        # إجمالي الأصناف
        self.total_items_label = ttk.Label(
            stats_frame,
            text="إجمالي الأصناف: 0",
            font=("Tahoma", 10, "bold")
        )
        self.total_items_label.pack(side='right', padx=20)
        
        # الأصناف منخفضة المخزون
        self.low_stock_label = ttk.Label(
            stats_frame,
            text="مخزون منخفض: 0",
            font=("Tahoma", 10, "bold"),
            foreground="#F39C12"
        )
        self.low_stock_label.pack(side='right', padx=20)
        
        # الأصناف نافدة المخزون
        self.out_of_stock_label = ttk.Label(
            stats_frame,
            text="نفد المخزون: 0",
            font=("Tahoma", 10, "bold"),
            foreground="#E74C3C"
        )
        self.out_of_stock_label.pack(side='right', padx=20)
        
        # قيمة المخزون الإجمالية
        self.total_value_label = ttk.Label(
            stats_frame,
            text="القيمة الإجمالية: 0.00 ر.س",
            font=("Tahoma", 10, "bold"),
            foreground="#27AE60"
        )
        self.total_value_label.pack(side='left', padx=20)
    
    def _create_control_buttons(self, parent):
        """إنشاء أزرار التحكم"""
        buttons_frame = ttk.Frame(parent)
        buttons_frame.pack(fill='x')
        
        # زر الإغلاق
        ttk.Button(
            buttons_frame,
            text="إغلاق",
            command=self.dialog.destroy,
            width=15
        ).pack(side='left')
        
        # زر تصدير التقرير
        ttk.Button(
            buttons_frame,
            text="تصدير تقرير",
            command=self._export_inventory_report,
            width=15
        ).pack(side='right', padx=5)
        
        # زر تحديث
        ttk.Button(
            buttons_frame,
            text="تحديث",
            command=self._refresh_data,
            width=15
        ).pack(side='right', padx=5)
    
    def _load_inventory_data(self):
        """تحميل بيانات المخزون"""
        # مسح البيانات الحالية
        for item in self.inventory_tree.get_children():
            self.inventory_tree.delete(item)
        
        # إضافة البيانات التجريبية
        for item in self.sample_inventory:
            # حساب القيمة الإجمالية
            total_value = item['current_stock'] * item['cost_price']
            
            # تحديد لون الصف حسب الحالة
            tags = []
            if item['status'] == 'نفد المخزون':
                tags = ['out_of_stock']
            elif item['status'] == 'مخزون منخفض':
                tags = ['low_stock']
            
            # إدراج الصف
            self.inventory_tree.insert('', 'end', values=(
                item['id'],
                item['name'],
                f"{item['current_stock']:.1f}",
                f"{item['minimum_stock']:.1f}",
                item['unit'],
                f"{item['cost_price']:.2f}",
                f"{total_value:.2f}",
                item['status']
            ), tags=tags)
        
        # تنسيق الألوان
        self.inventory_tree.tag_configure('out_of_stock', background='#FADBD8', foreground='#E74C3C')
        self.inventory_tree.tag_configure('low_stock', background='#FCF3CF', foreground='#F39C12')
        
        # تحديث الإحصائيات
        self._update_statistics()
    
    def _update_statistics(self):
        """تحديث الإحصائيات"""
        total_items = len(self.sample_inventory)
        low_stock_count = len([item for item in self.sample_inventory if item['status'] == 'مخزون منخفض'])
        out_of_stock_count = len([item for item in self.sample_inventory if item['status'] == 'نفد المخزون'])
        
        # حساب القيمة الإجمالية
        total_value = sum(item['current_stock'] * item['cost_price'] for item in self.sample_inventory)
        
        # تحديث التسميات
        self.total_items_label.config(text=f"إجمالي الأصناف: {total_items}")
        self.low_stock_label.config(text=f"مخزون منخفض: {low_stock_count}")
        self.out_of_stock_label.config(text=f"نفد المخزون: {out_of_stock_count}")
        self.total_value_label.config(text=f"القيمة الإجمالية: {total_value:.2f} ر.س")
    
    def _on_search(self, event=None):
        """معالج البحث"""
        search_term = self.search_var.get().lower()
        
        # مسح البيانات الحالية
        for item in self.inventory_tree.get_children():
            self.inventory_tree.delete(item)
        
        # تصفية وإعادة إدراج البيانات
        for item in self.sample_inventory:
            if not search_term or search_term in item['name'].lower():
                total_value = item['current_stock'] * item['cost_price']
                
                tags = []
                if item['status'] == 'نفد المخزون':
                    tags = ['out_of_stock']
                elif item['status'] == 'مخزون منخفض':
                    tags = ['low_stock']
                
                self.inventory_tree.insert('', 'end', values=(
                    item['id'],
                    item['name'],
                    f"{item['current_stock']:.1f}",
                    f"{item['minimum_stock']:.1f}",
                    item['unit'],
                    f"{item['cost_price']:.2f}",
                    f"{total_value:.2f}",
                    item['status']
                ), tags=tags)
        
        # إعادة تطبيق تنسيق الألوان
        self.inventory_tree.tag_configure('out_of_stock', background='#FADBD8', foreground='#E74C3C')
        self.inventory_tree.tag_configure('low_stock', background='#FCF3CF', foreground='#F39C12')
    
    def _on_item_double_click(self, event):
        """معالج النقر المزدوج على العنصر"""
        self._edit_inventory_item()
    
    def _get_selected_item(self):
        """الحصول على العنصر المحدد"""
        selection = self.inventory_tree.selection()
        if not selection:
            return None
        
        item_id = int(self.inventory_tree.item(selection[0])['values'][0])
        
        # البحث عن العنصر في البيانات
        for item in self.sample_inventory:
            if item['id'] == item_id:
                return item
        
        return None
    
    def _add_inventory_item(self):
        """إضافة صنف جديد للمخزون"""
        dialog = InventoryItemDialog(self.dialog, None)
        result = dialog.show()
        
        if result:
            # إضافة العنصر الجديد (تجريبي)
            new_id = max(item['id'] for item in self.sample_inventory) + 1
            new_item = {
                'id': new_id,
                'name': result['name'],
                'current_stock': result['current_stock'],
                'minimum_stock': result['minimum_stock'],
                'unit': result['unit'],
                'cost_price': result['cost_price'],
                'status': 'متوفر' if result['current_stock'] > result['minimum_stock'] else 'مخزون منخفض'
            }
            self.sample_inventory.append(new_item)
            self._refresh_data()
    
    def _edit_inventory_item(self):
        """تعديل صنف المخزون"""
        selected_item = self._get_selected_item()
        if not selected_item:
            messagebox.showwarning("تحذير", "يرجى اختيار صنف للتعديل")
            return
        
        dialog = InventoryItemDialog(self.dialog, selected_item)
        result = dialog.show()
        
        if result:
            # تحديث العنصر (تجريبي)
            for i, item in enumerate(self.sample_inventory):
                if item['id'] == selected_item['id']:
                    self.sample_inventory[i].update(result)
                    self.sample_inventory[i]['status'] = 'متوفر' if result['current_stock'] > result['minimum_stock'] else 'مخزون منخفض'
                    break
            self._refresh_data()
    
    def _update_stock(self):
        """تحديث كمية المخزون"""
        selected_item = self._get_selected_item()
        if not selected_item:
            messagebox.showwarning("تحذير", "يرجى اختيار صنف لتحديث كميته")
            return
        
        dialog = StockUpdateDialog(self.dialog, selected_item)
        result = dialog.show()
        
        if result:
            # تحديث الكمية (تجريبي)
            for i, item in enumerate(self.sample_inventory):
                if item['id'] == selected_item['id']:
                    self.sample_inventory[i]['current_stock'] = result['new_stock']
                    self.sample_inventory[i]['status'] = 'متوفر' if result['new_stock'] > item['minimum_stock'] else 'مخزون منخفض'
                    break
            self._refresh_data()
    
    def _refresh_data(self):
        """تحديث البيانات"""
        self._load_inventory_data()
    
    def _export_inventory_report(self):
        """تصدير تقرير المخزون"""
        messagebox.showinfo("تصدير", "سيتم إضافة وظيفة التصدير قريباً")


class InventoryItemDialog:
    """نافذة إضافة/تعديل صنف المخزون"""
    
    def __init__(self, parent, item_data=None):
        """تهيئة النافذة"""
        self.parent = parent
        self.item_data = item_data
        self.dialog = None
        self.result = None
        
        # متغيرات النموذج
        self.name_var = tk.StringVar()
        self.current_stock_var = tk.DoubleVar()
        self.minimum_stock_var = tk.DoubleVar()
        self.unit_var = tk.StringVar(value="قطعة")
        self.cost_price_var = tk.DoubleVar()
        
        # تحميل البيانات إذا كان تعديل
        if item_data:
            self.name_var.set(item_data.get('name', ''))
            self.current_stock_var.set(item_data.get('current_stock', 0))
            self.minimum_stock_var.set(item_data.get('minimum_stock', 0))
            self.unit_var.set(item_data.get('unit', 'قطعة'))
            self.cost_price_var.set(item_data.get('cost_price', 0))
    
    def show(self):
        """عرض النافذة"""
        self.dialog = tk.Toplevel(self.parent)
        title = "تعديل صنف المخزون" if self.item_data else "إضافة صنف جديد"
        self.dialog.title(title)
        self.dialog.geometry("400x400")
        self.dialog.resizable(False, False)
        
        # توسيط النافذة
        self._center_dialog()
        
        # جعل النافذة مودالية
        self.dialog.transient(self.parent)
        self.dialog.grab_set()
        
        # إنشاء الواجهة
        self._create_widgets()
        
        # انتظار النتيجة
        self.dialog.wait_window()
        
        return self.result
    
    def _center_dialog(self):
        """توسيط النافذة"""
        self.dialog.update_idletasks()
        
        parent_x = self.parent.winfo_rootx()
        parent_y = self.parent.winfo_rooty()
        parent_width = self.parent.winfo_width()
        parent_height = self.parent.winfo_height()
        
        x = parent_x + (parent_width - 400) // 2
        y = parent_y + (parent_height - 400) // 2
        
        self.dialog.geometry(f"400x400+{x}+{y}")
    
    def _create_widgets(self):
        """إنشاء عناصر الواجهة"""
        # الإطار الرئيسي
        main_frame = ttk.Frame(self.dialog, padding="20")
        main_frame.pack(fill='both', expand=True)
        
        # العنوان
        title = "تعديل صنف المخزون" if self.item_data else "إضافة صنف جديد"
        title_label = ttk.Label(
            main_frame,
            text=f"📦 {title}",
            font=("Tahoma", 14, "bold"),
            foreground="#2C3E50"
        )
        title_label.pack(pady=(0, 20))
        
        # نموذج البيانات
        form_frame = ttk.Frame(main_frame)
        form_frame.pack(fill='both', expand=True)
        
        # اسم الصنف
        name_frame = ttk.Frame(form_frame)
        name_frame.pack(fill='x', pady=5)
        
        ttk.Label(name_frame, text="اسم الصنف:", font=("Tahoma", 10, "bold")).pack(anchor='e', pady=2)
        ttk.Entry(name_frame, textvariable=self.name_var, width=40).pack(fill='x', pady=2)
        
        # الكمية الحالية
        current_stock_frame = ttk.Frame(form_frame)
        current_stock_frame.pack(fill='x', pady=5)
        
        ttk.Label(current_stock_frame, text="الكمية الحالية:", font=("Tahoma", 10, "bold")).pack(anchor='e', pady=2)
        ttk.Entry(current_stock_frame, textvariable=self.current_stock_var, width=40).pack(fill='x', pady=2)
        
        # الحد الأدنى
        minimum_stock_frame = ttk.Frame(form_frame)
        minimum_stock_frame.pack(fill='x', pady=5)
        
        ttk.Label(minimum_stock_frame, text="الحد الأدنى:", font=("Tahoma", 10, "bold")).pack(anchor='e', pady=2)
        ttk.Entry(minimum_stock_frame, textvariable=self.minimum_stock_var, width=40).pack(fill='x', pady=2)
        
        # الوحدة
        unit_frame = ttk.Frame(form_frame)
        unit_frame.pack(fill='x', pady=5)
        
        ttk.Label(unit_frame, text="الوحدة:", font=("Tahoma", 10, "bold")).pack(anchor='e', pady=2)
        unit_combo = ttk.Combobox(
            unit_frame,
            textvariable=self.unit_var,
            values=["قطعة", "كيلوجرام", "لتر", "علبة", "كيس", "حبة"],
            width=37
        )
        unit_combo.pack(fill='x', pady=2)
        
        # سعر التكلفة
        cost_frame = ttk.Frame(form_frame)
        cost_frame.pack(fill='x', pady=5)
        
        ttk.Label(cost_frame, text="سعر التكلفة:", font=("Tahoma", 10, "bold")).pack(anchor='e', pady=2)
        ttk.Entry(cost_frame, textvariable=self.cost_price_var, width=40).pack(fill='x', pady=2)
        
        # أزرار التحكم
        buttons_frame = ttk.Frame(main_frame)
        buttons_frame.pack(fill='x', pady=(20, 0))
        
        ttk.Button(
            buttons_frame,
            text="إلغاء",
            command=self._on_cancel,
            width=15
        ).pack(side='left')
        
        ttk.Button(
            buttons_frame,
            text="حفظ",
            command=self._on_save,
            width=15
        ).pack(side='right')
    
    def _validate_form(self):
        """التحقق من صحة البيانات"""
        if not self.name_var.get().strip():
            messagebox.showerror("خطأ", "يرجى إدخال اسم الصنف")
            return False
        
        if self.current_stock_var.get() < 0:
            messagebox.showerror("خطأ", "الكمية الحالية لا يمكن أن تكون سالبة")
            return False
        
        if self.minimum_stock_var.get() < 0:
            messagebox.showerror("خطأ", "الحد الأدنى لا يمكن أن يكون سالباً")
            return False
        
        if self.cost_price_var.get() < 0:
            messagebox.showerror("خطأ", "سعر التكلفة لا يمكن أن يكون سالباً")
            return False
        
        return True
    
    def _on_save(self):
        """حفظ البيانات"""
        if not self._validate_form():
            return
        
        self.result = {
            'name': self.name_var.get().strip(),
            'current_stock': self.current_stock_var.get(),
            'minimum_stock': self.minimum_stock_var.get(),
            'unit': self.unit_var.get().strip(),
            'cost_price': self.cost_price_var.get()
        }
        
        self.dialog.destroy()
    
    def _on_cancel(self):
        """إلغاء العملية"""
        self.result = None
        self.dialog.destroy()


class StockUpdateDialog:
    """نافذة تحديث المخزون"""
    
    def __init__(self, parent, item_data):
        """تهيئة النافذة"""
        self.parent = parent
        self.item_data = item_data
        self.dialog = None
        self.result = None
        
        # متغيرات النموذج
        self.operation_var = tk.StringVar(value="add")
        self.quantity_var = tk.DoubleVar()
        self.reason_var = tk.StringVar()
    
    def show(self):
        """عرض النافذة"""
        self.dialog = tk.Toplevel(self.parent)
        self.dialog.title("تحديث المخزون")
        self.dialog.geometry("400x350")
        self.dialog.resizable(False, False)
        
        # توسيط النافذة
        self._center_dialog()
        
        # جعل النافذة مودالية
        self.dialog.transient(self.parent)
        self.dialog.grab_set()
        
        # إنشاء الواجهة
        self._create_widgets()
        
        # انتظار النتيجة
        self.dialog.wait_window()
        
        return self.result
    
    def _center_dialog(self):
        """توسيط النافذة"""
        self.dialog.update_idletasks()
        
        parent_x = self.parent.winfo_rootx()
        parent_y = self.parent.winfo_rooty()
        parent_width = self.parent.winfo_width()
        parent_height = self.parent.winfo_height()
        
        x = parent_x + (parent_width - 400) // 2
        y = parent_y + (parent_height - 350) // 2
        
        self.dialog.geometry(f"400x350+{x}+{y}")
    
    def _create_widgets(self):
        """إنشاء عناصر الواجهة"""
        # الإطار الرئيسي
        main_frame = ttk.Frame(self.dialog, padding="20")
        main_frame.pack(fill='both', expand=True)
        
        # العنوان
        title_label = ttk.Label(
            main_frame,
            text="📈 تحديث المخزون",
            font=("Tahoma", 14, "bold"),
            foreground="#2C3E50"
        )
        title_label.pack(pady=(0, 20))
        
        # معلومات الصنف
        info_frame = ttk.LabelFrame(main_frame, text="معلومات الصنف", padding="10")
        info_frame.pack(fill='x', pady=(0, 15))
        
        ttk.Label(info_frame, text=f"الصنف: {self.item_data['name']}", font=("Tahoma", 10, "bold")).pack(anchor='e')
        ttk.Label(info_frame, text=f"الكمية الحالية: {self.item_data['current_stock']:.1f} {self.item_data['unit']}", font=("Tahoma", 10)).pack(anchor='e')
        ttk.Label(info_frame, text=f"الحد الأدنى: {self.item_data['minimum_stock']:.1f} {self.item_data['unit']}", font=("Tahoma", 10)).pack(anchor='e')
        
        # نوع العملية
        operation_frame = ttk.LabelFrame(main_frame, text="نوع العملية", padding="10")
        operation_frame.pack(fill='x', pady=(0, 15))
        
        ttk.Radiobutton(operation_frame, text="إضافة للمخزون", variable=self.operation_var, value="add").pack(anchor='e')
        ttk.Radiobutton(operation_frame, text="خصم من المخزون", variable=self.operation_var, value="subtract").pack(anchor='e')
        ttk.Radiobutton(operation_frame, text="تعديل مباشر", variable=self.operation_var, value="set").pack(anchor='e')
        
        # الكمية
        quantity_frame = ttk.Frame(main_frame)
        quantity_frame.pack(fill='x', pady=(0, 15))
        
        ttk.Label(quantity_frame, text="الكمية:", font=("Tahoma", 10, "bold")).pack(anchor='e', pady=2)
        ttk.Entry(quantity_frame, textvariable=self.quantity_var, width=20).pack(anchor='e', pady=2)
        
        # السبب
        reason_frame = ttk.Frame(main_frame)
        reason_frame.pack(fill='x', pady=(0, 15))
        
        ttk.Label(reason_frame, text="السبب:", font=("Tahoma", 10, "bold")).pack(anchor='e', pady=2)
        reason_combo = ttk.Combobox(
            reason_frame,
            textvariable=self.reason_var,
            values=["شراء جديد", "مبيعات", "تلف", "انتهاء صلاحية", "جرد", "تصحيح", "أخرى"],
            width=37
        )
        reason_combo.pack(fill='x', pady=2)
        
        # أزرار التحكم
        buttons_frame = ttk.Frame(main_frame)
        buttons_frame.pack(fill='x')
        
        ttk.Button(
            buttons_frame,
            text="إلغاء",
            command=self._on_cancel,
            width=15
        ).pack(side='left')
        
        ttk.Button(
            buttons_frame,
            text="تحديث",
            command=self._on_update,
            width=15
        ).pack(side='right')
    
    def _on_update(self):
        """تحديث المخزون"""
        if self.quantity_var.get() <= 0:
            messagebox.showerror("خطأ", "يرجى إدخال كمية صحيحة")
            return
        
        current_stock = self.item_data['current_stock']
        quantity = self.quantity_var.get()
        operation = self.operation_var.get()
        
        # حساب الكمية الجديدة
        if operation == "add":
            new_stock = current_stock + quantity
        elif operation == "subtract":
            new_stock = max(0, current_stock - quantity)
        else:  # set
            new_stock = quantity
        
        messagebox.showinfo("نجح", f"تم تحديث المخزون بنجاح\nالكمية الجديدة: {new_stock:.1f}")
        
        self.result = {'new_stock': new_stock}
        self.dialog.destroy()
    
    def _on_cancel(self):
        """إلغاء العملية"""
        self.result = None
        self.dialog.destroy()