#!/usr/bin/env python3
# -*- coding: utf-8 -*-
"""
تقارير المبيعات - نظام نقطة البيع
Sales Reports for POS System
"""

import tkinter as tk
from tkinter import ttk, messagebox
from datetime import datetime, timedelta
from typing import Dict, List
import json
import os

class SalesReportWindow:
    """نافذة تقارير المبيعات"""
    
    def __init__(self, parent, db_manager, config):
        """تهيئة نافذة التقارير"""
        self.parent = parent
        self.db_manager = db_manager
        self.config = config
        
        self.window = None
        self.report_data = {}
        
        # متغيرات التواريخ
        self.start_date_var = tk.StringVar(value=datetime.now().strftime('%Y-%m-%d'))
        self.end_date_var = tk.StringVar(value=datetime.now().strftime('%Y-%m-%d'))
        self.report_type_var = tk.StringVar(value='daily')
    
    def show(self):
        """عرض نافذة التقارير"""
        self.window = tk.Toplevel(self.parent)
        self.window.title("تقارير المبيعات")
        self.window.geometry("900x700")
        self.window.resizable(True, True)
        
        # توسيط النافذة
        self._center_window()
        
        # جعل النافذة مودالية
        self.window.transient(self.parent)
        self.window.grab_set()
        
        # إنشاء الواجهة
        self._create_widgets()
        
        # تحميل تقرير اليوم افتراضياً
        self._generate_report()
    
    def _center_window(self):
        """توسيط النافذة"""
        self.window.update_idletasks()
        
        parent_x = self.parent.winfo_rootx()
        parent_y = self.parent.winfo_rooty()
        parent_width = self.parent.winfo_width()
        parent_height = self.parent.winfo_height()
        
        x = parent_x + (parent_width - 900) // 2
        y = parent_y + (parent_height - 700) // 2
        
        self.window.geometry(f"900x700+{x}+{y}")
    
    def _create_widgets(self):
        """إنشاء عناصر الواجهة"""
        # الإطار الرئيسي
        main_frame = ttk.Frame(self.window, padding="10")
        main_frame.pack(fill='both', expand=True)
        
        # عنوان التقرير
        title_label = ttk.Label(
            main_frame,
            text="📊 تقارير المبيعات",
            font=("Tahoma", 18, "bold"),
            foreground="#2C3E50"
        )
        title_label.pack(pady=(0, 20))
        
        # إطار التحكم
        self._create_control_frame(main_frame)
        
        # إطار التقرير
        self._create_report_frame(main_frame)
        
        # أزرار العمليات
        self._create_action_buttons(main_frame)
    
    def _create_control_frame(self, parent):
        """إنشاء إطار التحكم"""
        control_frame = ttk.LabelFrame(parent, text="خيارات التقرير", padding="15")
        control_frame.pack(fill='x', pady=(0, 20))
        
        # الصف الأول - نوع التقرير
        row1_frame = ttk.Frame(control_frame)
        row1_frame.pack(fill='x', pady=5)
        
        ttk.Label(row1_frame, text="نوع التقرير:", font=("Tahoma", 11, "bold")).pack(side='right', padx=(0, 10))
        
        report_types = [
            ('daily', 'يومي'),
            ('weekly', 'أسبوعي'),
            ('monthly', 'شهري'),
            ('custom', 'مخصص')
        ]
        
        for value, text in report_types:
            ttk.Radiobutton(
                row1_frame,
                text=text,
                variable=self.report_type_var,
                value=value,
                command=self._on_report_type_changed
            ).pack(side='right', padx=10)
        
        # الصف الثاني - التواريخ
        self.date_frame = ttk.Frame(control_frame)
        self.date_frame.pack(fill='x', pady=5)
        
        ttk.Label(self.date_frame, text="من تاريخ:", font=("Tahoma", 11)).pack(side='right', padx=(0, 5))
        
        self.start_date_entry = ttk.Entry(
            self.date_frame,
            textvariable=self.start_date_var,
            width=12,
            justify='center'
        )
        self.start_date_entry.pack(side='right', padx=5)
        
        ttk.Label(self.date_frame, text="إلى تاريخ:", font=("Tahoma", 11)).pack(side='right', padx=(20, 5))
        
        self.end_date_entry = ttk.Entry(
            self.date_frame,
            textvariable=self.end_date_var,
            width=12,
            justify='center'
        )
        self.end_date_entry.pack(side='right', padx=5)
        
        # زر إنشاء التقرير
        ttk.Button(
            self.date_frame,
            text="إنشاء التقرير",
            command=self._generate_report,
            style="Primary.TButton"
        ).pack(side='left', padx=20)
        
        # تعطيل حقول التاريخ افتراضياً
        self._on_report_type_changed()
    
    def _create_report_frame(self, parent):
        """إنشاء إطار التقرير"""
        report_frame = ttk.LabelFrame(parent, text="نتائج التقرير", padding="15")
        report_frame.pack(fill='both', expand=True, pady=(0, 20))
        
        # إنشاء دفتر الملاحظات للتبويبات
        self.notebook = ttk.Notebook(report_frame)
        self.notebook.pack(fill='both', expand=True)
        
        # تبويب الملخص
        self._create_summary_tab()
        
        # تبويب التفاصيل
        self._create_details_tab()
        
        # تبويب الإحصائيات
        self._create_statistics_tab()
    
    def _create_summary_tab(self):
        """إنشاء تبويب الملخص"""
        summary_frame = ttk.Frame(self.notebook)
        self.notebook.add(summary_frame, text="الملخص العام")
        
        # إطار الإحصائيات الرئيسية
        main_stats_frame = ttk.Frame(summary_frame, padding="10")
        main_stats_frame.pack(fill='x')
        
        # بطاقات الإحصائيات
        self._create_stat_cards(main_stats_frame)
        
        # رسم بياني (مبسط)
        chart_frame = ttk.LabelFrame(summary_frame, text="المبيعات حسب طريقة الدفع", padding="15")
        chart_frame.pack(fill='both', expand=True, padx=10, pady=10)
        
        self.chart_canvas = tk.Canvas(chart_frame, height=200, bg='white')
        self.chart_canvas.pack(fill='both', expand=True)
    
    def _create_stat_cards(self, parent):
        """إنشاء بطاقات الإحصائيات"""
        # بطاقة إجمالي المبيعات
        sales_card = ttk.LabelFrame(parent, text="إجمالي المبيعات", padding="10")
        sales_card.pack(side='left', fill='both', expand=True, padx=5)
        
        self.total_sales_label = ttk.Label(
            sales_card,
            text="0.00 ر.س",
            font=("Tahoma", 16, "bold"),
            foreground="#27AE60"
        )
        self.total_sales_label.pack()
        
        # بطاقة عدد الطلبات
        orders_card = ttk.LabelFrame(parent, text="عدد الطلبات", padding="10")
        orders_card.pack(side='left', fill='both', expand=True, padx=5)
        
        self.total_orders_label = ttk.Label(
            orders_card,
            text="0",
            font=("Tahoma", 16, "bold"),
            foreground="#3498DB"
        )
        self.total_orders_label.pack()
        
        # بطاقة متوسط الطلب
        avg_card = ttk.LabelFrame(parent, text="متوسط قيمة الطلب", padding="10")
        avg_card.pack(side='left', fill='both', expand=True, padx=5)
        
        self.avg_order_label = ttk.Label(
            avg_card,
            text="0.00 ر.س",
            font=("Tahoma", 16, "bold"),
            foreground="#F39C12"
        )
        self.avg_order_label.pack()
        
        # بطاقة الضرائب
        tax_card = ttk.LabelFrame(parent, text="إجمالي الضرائب", padding="10")
        tax_card.pack(side='left', fill='both', expand=True, padx=5)
        
        self.total_tax_label = ttk.Label(
            tax_card,
            text="0.00 ر.س",
            font=("Tahoma", 16, "bold"),
            foreground="#E74C3C"
        )
        self.total_tax_label.pack()
    
    def _create_details_tab(self):
        """إنشاء تبويب التفاصيل"""
        details_frame = ttk.Frame(self.notebook)
        self.notebook.add(details_frame, text="التفاصيل")
        
        # جدول التفاصيل
        columns = ('order_id', 'time', 'items', 'total', 'payment_method', 'cashier')
        
        self.details_tree = ttk.Treeview(details_frame, columns=columns, show='headings', height=15)
        
        # تحديد عناوين الأعمدة
        self.details_tree.heading('order_id', text='رقم الطلب', anchor='center')
        self.details_tree.heading('time', text='الوقت', anchor='center')
        self.details_tree.heading('items', text='عدد الأصناف', anchor='center')
        self.details_tree.heading('total', text='المجموع', anchor='center')
        self.details_tree.heading('payment_method', text='طريقة الدفع', anchor='center')
        self.details_tree.heading('cashier', text='الكاشير', anchor='center')
        
        # تحديد عرض الأعمدة
        self.details_tree.column('order_id', width=100, anchor='center')
        self.details_tree.column('time', width=150, anchor='center')
        self.details_tree.column('items', width=100, anchor='center')
        self.details_tree.column('total', width=120, anchor='center')
        self.details_tree.column('payment_method', width=120, anchor='center')
        self.details_tree.column('cashier', width=150, anchor='center')
        
        # شريط التمرير
        details_scrollbar = ttk.Scrollbar(details_frame, orient='vertical', command=self.details_tree.yview)
        self.details_tree.configure(yscrollcommand=details_scrollbar.set)
        
        # تخطيط الجدول
        self.details_tree.pack(side='left', fill='both', expand=True)
        details_scrollbar.pack(side='right', fill='y')
    
    def _create_statistics_tab(self):
        """إنشاء تبويب الإحصائيات"""
        stats_frame = ttk.Frame(self.notebook)
        self.notebook.add(stats_frame, text="إحصائيات متقدمة")
        
        # إطار طرق الدفع
        payment_frame = ttk.LabelFrame(stats_frame, text="المبيعات حسب طريقة الدفع", padding="15")
        payment_frame.pack(fill='x', padx=10, pady=5)
        
        # جدول طرق الدفع
        payment_columns = ('method', 'count', 'amount', 'percentage')
        self.payment_tree = ttk.Treeview(payment_frame, columns=payment_columns, show='headings', height=6)
        
        self.payment_tree.heading('method', text='طريقة الدفع', anchor='center')
        self.payment_tree.heading('count', text='عدد المعاملات', anchor='center')
        self.payment_tree.heading('amount', text='المبلغ', anchor='center')
        self.payment_tree.heading('percentage', text='النسبة المئوية', anchor='center')
        
        self.payment_tree.column('method', width=150, anchor='center')
        self.payment_tree.column('count', width=120, anchor='center')
        self.payment_tree.column('amount', width=150, anchor='center')
        self.payment_tree.column('percentage', width=120, anchor='center')
        
        self.payment_tree.pack(fill='x')
        
        # إطار الأصناف الأكثر مبيعاً
        items_frame = ttk.LabelFrame(stats_frame, text="الأصناف الأكثر مبيعاً", padding="15")
        items_frame.pack(fill='both', expand=True, padx=10, pady=5)
        
        # جدول الأصناف
        items_columns = ('item_name', 'quantity', 'revenue', 'avg_price')
        self.items_tree = ttk.Treeview(items_frame, columns=items_columns, show='headings')
        
        self.items_tree.heading('item_name', text='اسم الصنف', anchor='center')
        self.items_tree.heading('quantity', text='الكمية المباعة', anchor='center')
        self.items_tree.heading('revenue', text='الإيرادات', anchor='center')
        self.items_tree.heading('avg_price', text='متوسط السعر', anchor='center')
        
        self.items_tree.column('item_name', width=200, anchor='center')
        self.items_tree.column('quantity', width=120, anchor='center')
        self.items_tree.column('revenue', width=150, anchor='center')
        self.items_tree.column('avg_price', width=120, anchor='center')
        
        items_scrollbar = ttk.Scrollbar(items_frame, orient='vertical', command=self.items_tree.yview)
        self.items_tree.configure(yscrollcommand=items_scrollbar.set)
        
        self.items_tree.pack(side='left', fill='both', expand=True)
        items_scrollbar.pack(side='right', fill='y')
    
    def _create_action_buttons(self, parent):
        """إنشاء أزرار العمليات"""
        buttons_frame = ttk.Frame(parent)
        buttons_frame.pack(fill='x')
        
        # زر الإغلاق
        ttk.Button(
            buttons_frame,
            text="إغلاق",
            command=self.window.destroy,
            width=15
        ).pack(side='left')
        
        # زر الطباعة
        ttk.Button(
            buttons_frame,
            text="طباعة التقرير",
            command=self._print_report,
            style="Secondary.TButton",
            width=15
        ).pack(side='right', padx=5)
        
        # زر التصدير
        ttk.Button(
            buttons_frame,
            text="تصدير إلى Excel",
            command=self._export_to_excel,
            style="Secondary.TButton",
            width=15
        ).pack(side='right', padx=5)
        
        # زر الحفظ
        ttk.Button(
            buttons_frame,
            text="حفظ التقرير",
            command=self._save_report,
            style="Secondary.TButton",
            width=15
        ).pack(side='right', padx=5)
    
    def _on_report_type_changed(self):
        """معالج تغيير نوع التقرير"""
        report_type = self.report_type_var.get()
        
        if report_type == 'custom':
            self.start_date_entry.config(state='normal')
            self.end_date_entry.config(state='normal')
        else:
            self.start_date_entry.config(state='disabled')
            self.end_date_entry.config(state='disabled')
            
            # تحديث التواريخ حسب النوع
            today = datetime.now()
            
            if report_type == 'daily':
                self.start_date_var.set(today.strftime('%Y-%m-%d'))
                self.end_date_var.set(today.strftime('%Y-%m-%d'))
            elif report_type == 'weekly':
                start_of_week = today - timedelta(days=today.weekday())
                end_of_week = start_of_week + timedelta(days=6)
                self.start_date_var.set(start_of_week.strftime('%Y-%m-%d'))
                self.end_date_var.set(end_of_week.strftime('%Y-%m-%d'))
            elif report_type == 'monthly':
                start_of_month = today.replace(day=1)
                next_month = start_of_month.replace(month=start_of_month.month % 12 + 1)
                end_of_month = next_month - timedelta(days=1)
                self.start_date_var.set(start_of_month.strftime('%Y-%m-%d'))
                self.end_date_var.set(end_of_month.strftime('%Y-%m-%d'))
    
    def _generate_report(self):
        """إنشاء التقرير"""
        try:
            start_date = self.start_date_var.get()
            end_date = self.end_date_var.get()
            
            # التحقق من صحة التواريخ
            try:
                datetime.strptime(start_date, '%Y-%m-%d')
                datetime.strptime(end_date, '%Y-%m-%d')
            except ValueError:
                messagebox.showerror("خطأ", "تنسيق التاريخ غير صحيح. استخدم YYYY-MM-DD")
                return
            
            # الحصول على بيانات التقرير من قاعدة البيانات
            self.report_data = self._fetch_report_data(start_date, end_date)
            
            # تحديث الواجهة
            self._update_summary()
            self._update_details()
            self._update_statistics()
            self._draw_chart()
            
        except Exception as e:
            messagebox.showerror("خطأ", f"خطأ في إنشاء التقرير: {str(e)}")
    
    def _fetch_report_data(self, start_date: str, end_date: str) -> Dict:
        """الحصول على بيانات التقرير من قاعدة البيانات"""
        # هذه دالة مبسطة - في التطبيق الحقيقي ستستدعي قاعدة البيانات
        
        # بيانات تجريبية
        return {
            'total_sales': 15750.50,
            'total_orders': 127,
            'total_tax': 2205.07,
            'total_discount': 425.00,
            'cash_sales': 8900.25,
            'card_sales': 5320.75,
            'digital_sales': 1529.50,
            'orders': [
                {
                    'id': 'ORD001',
                    'time': '2024-01-15 14:30:00',
                    'items': 3,
                    'total': 125.50,
                    'payment_method': 'نقدي',
                    'cashier': 'محمد أحمد'
                },
                # المزيد من الطلبات...
            ],
            'top_items': [
                {'name': 'شاورما دجاج', 'quantity': 45, 'revenue': 1125.00},
                {'name': 'شاورما لحمة', 'quantity': 32, 'revenue': 960.00},
                {'name': 'دجاج مشوي', 'quantity': 28, 'revenue': 1120.00},
            ]
        }
    
    def _update_summary(self):
        """تحديث تبويب الملخص"""
        data = self.report_data
        
        # تحديث بطاقات الإحصائيات
        self.total_sales_label.config(text=f"{data.get('total_sales', 0):.2f} {self.config.currency}")
        self.total_orders_label.config(text=str(data.get('total_orders', 0)))
        
        # حساب متوسط قيمة الطلب
        avg_order = data.get('total_sales', 0) / max(data.get('total_orders', 1), 1)
        self.avg_order_label.config(text=f"{avg_order:.2f} {self.config.currency}")
        
        self.total_tax_label.config(text=f"{data.get('total_tax', 0):.2f} {self.config.currency}")
    
    def _update_details(self):
        """تحديث تبويب التفاصيل"""
        # مسح البيانات الحالية
        for item in self.details_tree.get_children():
            self.details_tree.delete(item)
        
        # إضافة البيانات الجديدة
        for order in self.report_data.get('orders', []):
            self.details_tree.insert('', 'end', values=(
                order.get('id', ''),
                order.get('time', ''),
                order.get('items', 0),
                f"{order.get('total', 0):.2f} {self.config.currency}",
                order.get('payment_method', ''),
                order.get('cashier', '')
            ))
    
    def _update_statistics(self):
        """تحديث تبويب الإحصائيات"""
        data = self.report_data
        
        # مسح جدول طرق الدفع
        for item in self.payment_tree.get_children():
            self.payment_tree.delete(item)
        
        # إضافة بيانات طرق الدفع
        total_sales = data.get('total_sales', 0)
        
        payment_methods = [
            ('نقدي', data.get('cash_sales', 0)),
            ('بطاقة', data.get('card_sales', 0)),
            ('رقمي', data.get('digital_sales', 0))
        ]
        
        for method, amount in payment_methods:
            if total_sales > 0:
                percentage = (amount / total_sales) * 100
            else:
                percentage = 0
            
            # عدد المعاملات (تقديري)
            count = int(amount / 50) if amount > 0 else 0
            
            self.payment_tree.insert('', 'end', values=(
                method,
                count,
                f"{amount:.2f} {self.config.currency}",
                f"{percentage:.1f}%"
            ))
        
        # مسح جدول الأصناف
        for item in self.items_tree.get_children():
            self.items_tree.delete(item)
        
        # إضافة بيانات الأصناف
        for item in data.get('top_items', []):
            avg_price = item['revenue'] / max(item['quantity'], 1)
            
            self.items_tree.insert('', 'end', values=(
                item['name'],
                item['quantity'],
                f"{item['revenue']:.2f} {self.config.currency}",
                f"{avg_price:.2f} {self.config.currency}"
            ))
    
    def _draw_chart(self):
        """رسم الرسم البياني"""
        canvas = self.chart_canvas
        canvas.delete("all")
        
        # الحصول على أبعاد الكانفاس
        canvas.update()
        width = canvas.winfo_width()
        height = canvas.winfo_height()
        
        if width <= 1 or height <= 1:
            return
        
        # بيانات الرسم البياني
        data = self.report_data
        cash = data.get('cash_sales', 0)
        card = data.get('card_sales', 0)
        digital = data.get('digital_sales', 0)
        
        total = cash + card + digital
        
        if total == 0:
            canvas.create_text(width//2, height//2, text="لا توجد بيانات للعرض", font=("Tahoma", 12))
            return
        
        # رسم الأعمدة البيانية
        bar_width = width // 4
        max_height = height - 60
        
        # نقدي
        cash_height = int((cash / total) * max_height) if total > 0 else 0
        canvas.create_rectangle(
            bar_width * 0.5, height - 30 - cash_height,
            bar_width * 1.5, height - 30,
            fill="#27AE60", outline="#27AE60"
        )
        canvas.create_text(bar_width, height - 10, text="نقدي", font=("Tahoma", 10))
        canvas.create_text(bar_width, height - 40 - cash_height, text=f"{cash:.0f}", font=("Tahoma", 9))
        
        # بطاقة
        card_height = int((card / total) * max_height) if total > 0 else 0
        canvas.create_rectangle(
            bar_width * 1.7, height - 30 - card_height,
            bar_width * 2.7, height - 30,
            fill="#3498DB", outline="#3498DB"
        )
        canvas.create_text(bar_width * 2.2, height - 10, text="بطاقة", font=("Tahoma", 10))
        canvas.create_text(bar_width * 2.2, height - 40 - card_height, text=f"{card:.0f}", font=("Tahoma", 9))
        
        # رقمي
        digital_height = int((digital / total) * max_height) if total > 0 else 0
        canvas.create_rectangle(
            bar_width * 2.9, height - 30 - digital_height,
            bar_width * 3.9, height - 30,
            fill="#F39C12", outline="#F39C12"
        )
        canvas.create_text(bar_width * 3.4, height - 10, text="رقمي", font=("Tahoma", 10))
        canvas.create_text(bar_width * 3.4, height - 40 - digital_height, text=f"{digital:.0f}", font=("Tahoma", 9))
    
    def _print_report(self):
        """طباعة التقرير"""
        try:
            # إنشاء محتوى التقرير للطباعة
            report_content = self._generate_print_content()
            
            # هنا يمكن إضافة كود الطباعة الفعلي
            messagebox.showinfo("طباعة", "سيتم إضافة وظيفة الطباعة قريباً")
            
        except Exception as e:
            messagebox.showerror("خطأ", f"خطأ في طباعة التقرير: {str(e)}")
    
    def _export_to_excel(self):
        """تصدير التقرير إلى Excel"""
        try:
            # هنا يمكن إضافة كود التصدير إلى Excel
            messagebox.showinfo("تصدير", "سيتم إضافة وظيفة التصدير قريباً")
            
        except Exception as e:
            messagebox.showerror("خطأ", f"خطأ في تصدير التقرير: {str(e)}")
    
    def _save_report(self):
        """حفظ التقرير"""
        try:
            # إنشاء اسم الملف
            timestamp = datetime.now().strftime('%Y%m%d_%H%M%S')
            filename = f"sales_report_{timestamp}.json"
            
            # حفظ البيانات
            with open(filename, 'w', encoding='utf-8') as f:
                json.dump(self.report_data, f, ensure_ascii=False, indent=4)
            
            messagebox.showinfo("نجح", f"تم حفظ التقرير في الملف: {filename}")
            
        except Exception as e:
            messagebox.showerror("خطأ", f"خطأ في حفظ التقرير: {str(e)}")
    
    def _generate_print_content(self) -> str:
        """إنشاء محتوى التقرير للطباعة"""
        lines = []
        
        # رأس التقرير
        lines.append("=" * 60)
        lines.append("تقرير المبيعات - مطعم شاورما الدجاج")
        lines.append("=" * 60)
        lines.append(f"فترة التقرير: من {self.start_date_var.get()} إلى {self.end_date_var.get()}")
        lines.append(f"تاريخ الإنشاء: {datetime.now().strftime('%Y-%m-%d %H:%M:%S')}")
        lines.append("")
        
        # الملخص العام
        data = self.report_data
        lines.append("الملخص العام:")
        lines.append("-" * 20)
        lines.append(f"إجمالي المبيعات: {data.get('total_sales', 0):.2f} {self.config.currency}")
        lines.append(f"عدد الطلبات: {data.get('total_orders', 0)}")
        lines.append(f"متوسط قيمة الطلب: {data.get('total_sales', 0) / max(data.get('total_orders', 1), 1):.2f} {self.config.currency}")
        lines.append(f"إجمالي الضرائب: {data.get('total_tax', 0):.2f} {self.config.currency}")
        lines.append("")
        
        # المبيعات حسب طريقة الدفع
        lines.append("المبيعات حسب طريقة الدفع:")
        lines.append("-" * 30)
        lines.append(f"نقدي: {data.get('cash_sales', 0):.2f} {self.config.currency}")
        lines.append(f"بطاقة: {data.get('card_sales', 0):.2f} {self.config.currency}")
        lines.append(f"رقمي: {data.get('digital_sales', 0):.2f} {self.config.currency}")
        lines.append("")
        
        return '\n'.join(lines)