#!/usr/bin/env python3
# -*- coding: utf-8 -*-
"""
نظام نقطة البيع - مطعم شاورما الدجاج
Shawarma Chicken POS System
الملف الرئيسي للتطبيق
"""

import tkinter as tk
from tkinter import messagebox
import sys
import os
from typing import Optional, Dict, Any

# إضافة مسار المشروع إلى مسار البايثون
sys.path.append(os.path.dirname(os.path.abspath(__file__)))

from database.db_manager import DatabaseManager
from ui.modern_login_window import ModernLoginWindow
from ui.modern_main_window import ModernMainWindow
from ui.theme_manager import theme_manager
from utils.config import Config

class ShawarmaChickenPOS:
    """الكلاس الرئيسي لنظام نقطة البيع"""
    
    def __init__(self):
        """تهيئة التطبيق"""
        try:
            self.root = tk.Tk()

            # تعيين خصائص النافذة الرئيسية
            self.root.title("نظام نقطة البيع - شاورما الدجاج")
            self.root.geometry("1200x800")

            # توسيط النافذة
            self._center_main_window()

            # إخفاء النافذة الرئيسية مؤقتاً
            self.root.withdraw()

            # تهيئة قاعدة البيانات
            self.db_manager = DatabaseManager()
            self.db_manager.initialize_database()

            # تهيئة الإعدادات
            self.config = Config()

            # المتغيرات العامة
            self.current_user: Optional[Dict[str, Any]] = None
            self.login_window: Optional[Any] = None
            self.main_window: Optional[Any] = None

            # بدء التطبيق بنافذة تسجيل الدخول
            self.show_login()

        except Exception as e:
            print(f"خطأ في تهيئة التطبيق: {str(e)}")
            raise

    def _center_main_window(self) -> None:
        """توسيط النافذة الرئيسية"""
        self.root.update_idletasks()

        # الحصول على أبعاد الشاشة
        screen_width = self.root.winfo_screenwidth()
        screen_height = self.root.winfo_screenheight()

        # حساب الموقع
        window_width = 1200
        window_height = 800
        x = (screen_width - window_width) // 2
        y = (screen_height - window_height) // 2

        self.root.geometry(f"{window_width}x{window_height}+{x}+{y}")

    def show_login(self) -> None:
        """عرض نافذة تسجيل الدخول"""
        if self.main_window:
            self.main_window.destroy()
            self.main_window = None
        
        self.login_window = ModernLoginWindow(self.root, self)
        self.login_window.show()
    
    def on_login_success(self, user_data: Dict[str, Any]) -> None:
        """معالج نجاح تسجيل الدخول"""
        self.current_user = user_data
        
        # إغلاق نافذة تسجيل الدخول
        if self.login_window:
            self.login_window.destroy()
            self.login_window = None
        
        # عرض النافذة الرئيسية
        self.show_main_window()
    
    def show_main_window(self) -> None:
        """عرض النافذة الرئيسية للنظام"""
        if self.current_user:
            self.root.deiconify()  # إظهار النافذة الرئيسية
            self.main_window = ModernMainWindow(self.root, self, self.current_user)
            # النافذة الحديثة لا تحتاج show() منفصلة
    
    def logout(self) -> None:
        """تسجيل الخروج"""
        if messagebox.askyesno("تسجيل الخروج", "هل أنت متأكد من تسجيل الخروج؟"):
            self.current_user = None
            self.root.withdraw()
            self.show_login()
    
    def run(self) -> None:
        """تشغيل التطبيق"""
        try:
            # إعداد النافذة الرئيسية
            self._center_main_window()

            # إخفاء النافذة الرئيسية مؤقتاً
            self.root.withdraw()

            # عرض نافذة تسجيل الدخول
            self.show_login()

            # بدء حلقة الأحداث
            self.root.mainloop()
        except Exception as e:
            messagebox.showerror("خطأ", f"حدث خطأ في التطبيق: {str(e)}")
        finally:
            if self.db_manager:
                self.db_manager.close()

def main() -> None:
    """الدالة الرئيسية"""
    try:
        app = ShawarmaChickenPOS()
        app.run()
    except Exception as e:
        print(f"خطأ في بدء التطبيق: {str(e)}")
        sys.exit(1)

if __name__ == "__main__":
    main()