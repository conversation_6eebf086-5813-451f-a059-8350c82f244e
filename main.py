#!/usr/bin/env python3
# -*- coding: utf-8 -*-
"""
نظام نقطة البيع - مطعم شاورما الدجاج
Shawarma Chicken POS System
الملف الرئيسي للتطبيق
"""

import tkinter as tk
from tkinter import ttk, messagebox
import sys
import os

# إضافة مسار المشروع إلى مسار البايثون
sys.path.append(os.path.dirname(os.path.abspath(__file__)))

from database.db_manager import DatabaseManager
from ui.login_window import LoginWindow
from ui.main_window import MainWindow
from utils.config import Config

class ShawarmaChickenPOS:
    """الكلاس الرئيسي لنظام نقطة البيع"""
    
    def __init__(self):
        """تهيئة التطبيق"""
        self.root = tk.Tk()
        self.root.withdraw()  # إخفاء النافذة الرئيسية مؤقتاً
        
        # تهيئة قاعدة البيانات
        self.db_manager = DatabaseManager()
        self.db_manager.initialize_database()
        
        # تهيئة الإعدادات
        self.config = Config()
        
        # المتغيرات العامة
        self.current_user = None
        self.login_window = None
        self.main_window = None
        
        # بدء التطبيق بنافذة تسجيل الدخول
        self.show_login()
    
    def show_login(self):
        """عرض نافذة تسجيل الدخول"""
        if self.main_window:
            self.main_window.destroy()
            self.main_window = None
        
        self.login_window = LoginWindow(self.root, self)
        self.login_window.show()
    
    def on_login_success(self, user_data):
        """معالج نجاح تسجيل الدخول"""
        self.current_user = user_data
        
        # إغلاق نافذة تسجيل الدخول
        if self.login_window:
            self.login_window.destroy()
            self.login_window = None
        
        # عرض النافذة الرئيسية
        self.show_main_window()
    
    def show_main_window(self):
        """عرض النافذة الرئيسية للنظام"""
        self.root.deiconify()  # إظهار النافذة الرئيسية
        self.main_window = MainWindow(self.root, self)
        self.main_window.show()
    
    def logout(self):
        """تسجيل الخروج"""
        if messagebox.askyesno("تسجيل الخروج", "هل أنت متأكد من تسجيل الخروج؟"):
            self.current_user = None
            self.root.withdraw()
            self.show_login()
    
    def run(self):
        """تشغيل التطبيق"""
        try:
            self.root.mainloop()
        except Exception as e:
            messagebox.showerror("خطأ", f"حدث خطأ في التطبيق: {str(e)}")
        finally:
            if self.db_manager:
                self.db_manager.close()

def main():
    """الدالة الرئيسية"""
    try:
        app = ShawarmaChickenPOS()
        app.run()
    except Exception as e:
        print(f"خطأ في بدء التطبيق: {str(e)}")
        sys.exit(1)

if __name__ == "__main__":
    main()