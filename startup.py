#!/usr/bin/env python3
# -*- coding: utf-8 -*-
"""
تشغيل مباشر لنظام نقطة البيع
Direct startup for POS System
"""

import sys
import os
import traceback

def setup_environment():
    """إعداد البيئة"""
    # إضافة مسار المشروع
    project_path = os.path.dirname(os.path.abspath(__file__))
    if project_path not in sys.path:
        sys.path.insert(0, project_path)
    
    print(f"مسار المشروع: {project_path}")

def check_requirements():
    """فحص المتطلبات الأساسية"""
    print("فحص المتطلبات...")

    # فحص Python
    if sys.version_info < (3, 6):
        print(f"❌ يتطلب Python 3.6+، الحالي: {sys.version}")
        return False

    # فحص المكتبات
    required_modules = ['tkinter', 'sqlite3', 'json', 'hashlib', 'datetime']
    missing_modules = []

    for module in required_modules:
        try:
            __import__(module)
            print(f"✅ {module}")
        except ImportError:
            print(f"❌ {module} غير متوفر")
            missing_modules.append(module)

    if missing_modules:
        print(f"\n❌ المكتبات المفقودة: {', '.join(missing_modules)}")
        return False

    return True

def run_pos_system():
    """تشغيل نظام نقطة البيع"""
    try:
        print("🚀 تشغيل نظام نقطة البيع...")

        # فحص توفر واجهة المستخدم الرسومية
        try:
            import tkinter as tk
            root = tk.Tk()
            root.withdraw()  # إخفاء النافذة مؤقتاً
            root.destroy()
            print("✅ واجهة المستخدم الرسومية متوفرة")
        except Exception as gui_error:
            print(f"❌ واجهة المستخدم الرسومية غير متوفرة: {str(gui_error)}")
            print("💡 تأكد من تشغيل النظام في بيئة تدعم الواجهة الرسومية")
            return False

        # استيراد التطبيق الرئيسي
        from main import ShawarmaChickenPOS

        # إنشاء وتشغيل التطبيق
        print("📱 إنشاء التطبيق...")
        app = ShawarmaChickenPOS()
        print("✅ تم تحميل التطبيق")

        print("📱 فتح واجهة المستخدم...")
        app.run()

        print("👋 تم إغلاق النظام")
        return True

    except KeyboardInterrupt:
        print("\n⏹️ تم إيقاف النظام بواسطة المستخدم")
        return True

    except Exception as e:
        print(f"❌ خطأ في تشغيل النظام: {str(e)}")
        print("\n📋 تفاصيل الخطأ:")
        traceback.print_exc()
        return False

def main():
    """الدالة الرئيسية"""
    print("🍖 مرحباً بك في نظام نقطة البيع - مطعم شاورما الدجاج")
    print("=" * 60)
    
    # إعداد البيئة
    setup_environment()
    
    # فحص المتطلبات
    if not check_requirements():
        print("\n❌ فشل في فحص المتطلبات")
        input("اضغط Enter للخروج...")
        return
    
    print("\n✅ جميع المتطلبات متوفرة")
    
    # تشغيل النظام
    if run_pos_system():
        print("\n✅ تم الانتهاء بنجاح")
    else:
        print("\n❌ حدثت مشكلة أثناء التشغيل")
        input("اضغط Enter للخروج...")

if __name__ == "__main__":
    main()