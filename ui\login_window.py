#!/usr/bin/env python3
# -*- coding: utf-8 -*-
"""
نافذة تسجيل الدخول - نظام نقطة البيع
Login Window for POS System
"""

import tkinter as tk
from tkinter import ttk, messagebox
from typing import Optional
import threading
import time

class LoginWindow:
    """نافذة تسجيل الدخول"""
    
    def __init__(self, parent, app_instance):
        """تهيئة نافذة تسجيل الدخول"""
        self.parent = parent
        self.app = app_instance
        self.window = None
        self.login_attempts = 0
        self.max_attempts = 5
        
        # متغيرات النموذج
        self.username_var = tk.StringVar()
        self.password_var = tk.StringVar()
        self.remember_var = tk.BooleanVar()
        
        # عناصر الواجهة
        self.username_entry = None
        self.password_entry = None
        self.login_button = None
        self.status_label = None
        self.progress_bar = None
    
    def show(self):
        """عرض نافذة تسجيل الدخول"""
        self.window = tk.Toplevel(self.parent)
        self.window.title("تسجيل الدخول - شاورما الدجاج")
        self.window.geometry("500x600")
        self.window.resizable(False, False)

        # جعل النافذة في المنتصف
        self._center_window()

        # تعيين الرمز
        try:
            # يمكن إضافة رمز النافذة هنا
            pass
        except:
            pass

        # منع إغلاق النافذة بالـ X
        self.window.protocol("WM_DELETE_WINDOW", self._on_closing)

        # جعل النافذة دائماً في المقدمة
        self.window.transient(self.parent)

        # إنشاء الواجهة
        self._create_widgets()

        # جعل النافذة مرئية ومركزة
        self.window.deiconify()
        self.window.lift()
        self.window.focus_force()

        # تطبيق grab_set بعد التأكد من ظهور النافذة
        self.window.after(100, self._apply_grab)

        # التركيز على حقل اسم المستخدم
        self.window.after(200, lambda: self.username_entry.focus())

        # ربط مفتاح Enter
        self.window.bind('<Return>', lambda e: self._on_login())
    
    def _center_window(self):
        """توسيط النافذة على الشاشة"""
        self.window.update_idletasks()
        
        # الحصول على أبعاد الشاشة
        screen_width = self.window.winfo_screenwidth()
        screen_height = self.window.winfo_screenheight()
        
        # الحصول على أبعاد النافذة
        window_width = self.window.winfo_reqwidth()
        window_height = self.window.winfo_reqheight()
        
        # حساب الموقع
        x = (screen_width - window_width) // 2
        y = (screen_height - window_height) // 2
        
        self.window.geometry(f"500x600+{x}+{y}")

    def _apply_grab(self):
        """تطبيق grab_set بعد التأكد من ظهور النافذة"""
        try:
            if self.window and self.window.winfo_exists():
                self.window.grab_set()
        except:
            pass

    def _create_widgets(self):
        """إنشاء عناصر الواجهة"""
        # الإطار الرئيسي
        main_frame = ttk.Frame(self.window, padding="20")
        main_frame.pack(fill='both', expand=True)
        
        # شعار المطعم
        logo_frame = ttk.Frame(main_frame)
        logo_frame.pack(pady=(0, 30))
        
        try:
            # يمكن إضافة شعار هنا
            logo_label = ttk.Label(logo_frame, text="🍖", font=("Arial", 48))
            logo_label.pack()
        except:
            logo_label = ttk.Label(logo_frame, text="🍖", font=("Arial", 48))
            logo_label.pack()
        
        # عنوان التطبيق
        title_label = ttk.Label(
            logo_frame,
            text="مطعم شاورما الدجاج",
            font=("Tahoma", 20, "bold"),
            foreground="#2C3E50"
        )
        title_label.pack(pady=5)
        
        subtitle_label = ttk.Label(
            logo_frame,
            text="نظام نقطة البيع",
            font=("Tahoma", 14),
            foreground="#7F8C8D"
        )
        subtitle_label.pack()
        
        # نموذج تسجيل الدخول
        login_frame = ttk.LabelFrame(main_frame, text="تسجيل الدخول", padding="20")
        login_frame.pack(fill='x', pady=20)
        
        # حقل اسم المستخدم
        ttk.Label(login_frame, text="اسم المستخدم:", font=("Tahoma", 12)).pack(anchor='e', pady=(0, 5))
        self.username_entry = ttk.Entry(
            login_frame,
            textvariable=self.username_var,
            font=("Tahoma", 12),
            width=30,
            justify='right'
        )
        self.username_entry.pack(fill='x', pady=(0, 15))
        
        # حقل كلمة المرور
        ttk.Label(login_frame, text="كلمة المرور:", font=("Tahoma", 12)).pack(anchor='e', pady=(0, 5))
        self.password_entry = ttk.Entry(
            login_frame,
            textvariable=self.password_var,
            font=("Tahoma", 12),
            width=30,
            show="*",
            justify='right'
        )
        self.password_entry.pack(fill='x', pady=(0, 15))
        
        # خيار تذكر المستخدم
        remember_frame = ttk.Frame(login_frame)
        remember_frame.pack(fill='x', pady=(0, 20))
        
        ttk.Checkbutton(
            remember_frame,
            text="تذكر اسم المستخدم",
            variable=self.remember_var,
            command=self._on_remember_changed
        ).pack(anchor='e')
        
        # زر تسجيل الدخول
        button_frame = ttk.Frame(login_frame)
        button_frame.pack(fill='x')
        
        self.login_button = ttk.Button(
            button_frame,
            text="تسجيل الدخول",
            command=self._on_login,
            style="Accent.TButton"
        )
        self.login_button.pack(fill='x', pady=5)
        
        # شريط التقدم
        self.progress_bar = ttk.Progressbar(
            button_frame,
            mode='indeterminate',
            style="TProgressbar"
        )
        
        # تسمية الحالة
        self.status_label = ttk.Label(
            login_frame,
            text="",
            font=("Tahoma", 10),
            foreground="#E74C3C"
        )
        self.status_label.pack(pady=(10, 0))
        
        # معلومات النظام
        info_frame = ttk.Frame(main_frame)
        info_frame.pack(side='bottom', fill='x', pady=(20, 0))
        
        version_label = ttk.Label(
            info_frame,
            text="الإصدار 1.0.0",
            font=("Tahoma", 9),
            foreground="#95A5A6"
        )
        version_label.pack(side='left')
        
        help_label = ttk.Label(
            info_frame,
            text="للمساعدة: <EMAIL>",
            font=("Tahoma", 9),
            foreground="#95A5A6"
        )
        help_label.pack(side='right')
        
        # تطبيق التصميم
        self._apply_styles()
        
        # تحميل آخر اسم مستخدم محفوظ
        self._load_saved_username()
    
    def _apply_styles(self):
        """تطبيق الأنماط على الواجهة"""
        style = ttk.Style()
        
        # تحديد السمة
        style.theme_use('clam')
        
        # تخصيص الأزرار
        style.configure(
            "Accent.TButton",
            background="#3498DB",
            foreground="white",
            font=("Tahoma", 12, "bold"),
            padding=(10, 8)
        )
        
        style.map(
            "Accent.TButton",
            background=[('active', '#2980B9'), ('pressed', '#21618C')]
        )
        
        # تخصيص الحقول النصية
        style.configure(
            "TEntry",
            padding=8,
            relief="flat",
            borderwidth=1
        )
        
        style.map(
            "TEntry",
            focuscolor=[('!focus', '#BDC3C7'), ('focus', '#3498DB')]
        )
    
    def _load_saved_username(self):
        """تحميل اسم المستخدم المحفوظ"""
        try:
            # يمكن تحميل اسم المستخدم من ملف الإعدادات
            saved_username = self.app.config.get('login.saved_username', '')
            if saved_username:
                self.username_var.set(saved_username)
                self.remember_var.set(True)
                self.password_entry.focus()
        except:
            pass
    
    def _on_remember_changed(self):
        """معالج تغيير خيار التذكر"""
        if not self.remember_var.get():
            # حذف اسم المستخدم المحفوظ
            self.app.config.set('login.saved_username', '')
            self.app.config.save_settings()
    
    def _on_login(self):
        """معالج تسجيل الدخول"""
        username = self.username_var.get().strip()
        password = self.password_var.get().strip()
        
        # التحقق من صحة البيانات
        if not username:
            self._show_error("يرجى إدخال اسم المستخدم")
            self.username_entry.focus()
            return
        
        if not password:
            self._show_error("يرجى إدخال كلمة المرور")
            self.password_entry.focus()
            return
        
        # التحقق من عدد المحاولات
        if self.login_attempts >= self.max_attempts:
            self._show_error("تم تجاوز الحد الأقصى لمحاولات تسجيل الدخول")
            return
        
        # بدء عملية التحقق
        self._start_login_process(username, password)
    
    def _start_login_process(self, username: str, password: str):
        """بدء عملية تسجيل الدخول"""
        # تعطيل الواجهة
        self._set_loading_state(True)
        self._show_status("جاري التحقق من البيانات...")
        
        # تشغيل التحقق في خيط منفصل
        threading.Thread(
            target=self._authenticate_user,
            args=(username, password),
            daemon=True
        ).start()
    
    def _authenticate_user(self, username: str, password: str):
        """التحقق من صحة بيانات المستخدم"""
        try:
            # محاولة التحقق من المستخدم
            user_data = self.app.db_manager.authenticate_user(username, password)
            
            # محاكاة زمن التحقق (للتأثير البصري)
            time.sleep(1)
            
            # العودة للخيط الرئيسي
            self.window.after(0, self._handle_login_result, user_data, username)
            
        except Exception as e:
            self.window.after(0, self._handle_login_error, str(e))
    
    def _handle_login_result(self, user_data: Optional[dict], username: str):
        """معالجة نتيجة تسجيل الدخول"""
        self._set_loading_state(False)
        
        if user_data:
            # نجح تسجيل الدخول
            self._show_status("تم تسجيل الدخول بنجاح", success=True)
            
            # حفظ اسم المستخدم إذا كان مطلوباً
            if self.remember_var.get():
                self.app.config.set('login.saved_username', username)
                self.app.config.save_settings()
            
            # الانتقال للنافذة الرئيسية
            self.window.after(500, lambda: self.app.on_login_success(user_data))
            
        else:
            # فشل تسجيل الدخول
            self.login_attempts += 1
            remaining = self.max_attempts - self.login_attempts
            
            if remaining > 0:
                self._show_error(f"بيانات تسجيل الدخول غير صحيحة. المحاولات المتبقية: {remaining}")
            else:
                self._show_error("تم تجاوز الحد الأقصى للمحاولات. يرجى المحاولة لاحقاً")
                self._disable_login()
            
            # مسح كلمة المرور
            self.password_var.set("")
            self.password_entry.focus()
    
    def _handle_login_error(self, error_message: str):
        """معالجة خطأ في تسجيل الدخول"""
        self._set_loading_state(False)
        self._show_error(f"خطأ في الاتصال: {error_message}")
    
    def _set_loading_state(self, loading: bool):
        """تعيين حالة التحميل"""
        if loading:
            self.login_button.configure(state='disabled', text="جاري التحقق...")
            self.progress_bar.pack(fill='x', pady=(5, 0))
            self.progress_bar.start()
            self.username_entry.configure(state='disabled')
            self.password_entry.configure(state='disabled')
        else:
            self.login_button.configure(state='normal', text="تسجيل الدخول")
            self.progress_bar.stop()
            self.progress_bar.pack_forget()
            self.username_entry.configure(state='normal')
            self.password_entry.configure(state='normal')
    
    def _show_status(self, message: str, success: bool = False):
        """عرض رسالة الحالة"""
        color = "#27AE60" if success else "#2C3E50"
        self.status_label.configure(text=message, foreground=color)
    
    def _show_error(self, message: str):
        """عرض رسالة خطأ"""
        self.status_label.configure(text=message, foreground="#E74C3C")
        
        # اهتزاز النافذة للفت الانتباه
        self._shake_window()
    
    def _shake_window(self):
        """اهتزاز النافذة"""
        original_x = self.window.winfo_x()
        original_y = self.window.winfo_y()
        
        for i in range(10):
            offset = 5 if i % 2 == 0 else -5
            self.window.geometry(f"+{original_x + offset}+{original_y}")
            self.window.update()
            time.sleep(0.02)
        
        self.window.geometry(f"+{original_x}+{original_y}")
    
    def _disable_login(self):
        """تعطيل تسجيل الدخول"""
        self.login_button.configure(state='disabled')
        self.username_entry.configure(state='disabled')
        self.password_entry.configure(state='disabled')
        
        # إعادة تفعيل بعد دقيقة
        self.window.after(60000, self._enable_login)
    
    def _enable_login(self):
        """إعادة تفعيل تسجيل الدخول"""
        self.login_attempts = 0
        self.login_button.configure(state='normal')
        self.username_entry.configure(state='normal')
        self.password_entry.configure(state='normal')
        self._show_status("يمكنك المحاولة مرة أخرى")
    
    def _on_closing(self):
        """معالج إغلاق النافذة"""
        if messagebox.askyesno("إغلاق التطبيق", "هل تريد إغلاق التطبيق؟"):
            self.app.root.quit()
    
    def destroy(self):
        """تدمير النافذة"""
        if self.window:
            self.window.destroy()
            self.window = None