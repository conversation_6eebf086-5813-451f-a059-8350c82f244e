#!/bin/bash

# نظام نقطة البيع - شاورما الدجاج
# Shawarma Chicken POS System

echo "======================================"
echo " نظام نقطة البيع - شاورما الدجاج"
echo " Shawarma Chicken POS System"
echo "======================================"
echo

# تحديد مجلد العمل
cd "$(dirname "$0")"

# التحقق من وجود Python
if ! command -v python3 &> /dev/null; then
    if ! command -v python &> /dev/null; then
        echo "خطأ: Python غير مثبت أو غير موجود في PATH"
        echo "يرجى تثبيت Python 3.8 أو أحدث"
        exit 1
    else
        PYTHON_CMD="python"
    fi
else
    PYTHON_CMD="python3"
fi

# عرض معلومات Python
echo "تم العثور على Python:"
$PYTHON_CMD --version
echo

# التحقق من وجود الملف الرئيسي
if [ ! -f "main.py" ]; then
    echo "خطأ: ملف main.py غير موجود"
    echo "تأكد من أنك في المجلد الصحيح"
    exit 1
fi

# تشغيل النظام
echo "بدء تشغيل نظام نقطة البيع..."
echo
$PYTHON_CMD main.py

# في حالة حدوث خطأ
if [ $? -ne 0 ]; then
    echo
    echo "حدث خطأ أثناء تشغيل النظام"
    echo "يرجى مراجعة الأخطاء أعلاه"
    read -p "اضغط Enter للمتابعة..."
fi

echo
echo "تم إغلاق النظام"