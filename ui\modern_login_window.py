#!/usr/bin/env python3
# -*- coding: utf-8 -*-
"""
نافذة تسجيل الدخول الحديثة والمتطورة
Modern and Advanced Login Window
"""

import tkinter as tk
from tkinter import messagebox
from typing import Optional, Callable, Dict, Any
from .theme_manager import theme_manager
from .modern_components import ModernButton, ModernCard, ModernEntry, show_notification
import math
import threading
import time

class ModernLoginWindow:
    """نافذة تسجيل دخول حديثة ومتطورة"""
    
    def __init__(self, parent: tk.Tk, app):
        self.parent = parent
        self.app = app
        self.window: Optional[tk.Toplevel] = None
        self.username_entry: Optional[ModernEntry] = None
        self.password_entry: Optional[ModernEntry] = None
        self.remember_var: Optional[tk.BooleanVar] = None
        self.login_button: Optional[ModernButton] = None
        self.is_logging_in = False
        
        # متغيرات التأثيرات البصرية
        self.animation_step = 0
        self.gradient_colors = []
        
    def show(self):
        """عرض نافذة تسجيل الدخول"""
        self.window = tk.Toplevel(self.parent)
        self.window.title("🍖 تسجيل الدخول - شاورما الدجاج")
        self.window.geometry("450x650")
        self.window.resizable(False, False)
        
        # منع إغلاق النافذة بالـ X
        self.window.protocol("WM_DELETE_WINDOW", self._on_closing)
        
        # إنشاء الواجهة
        self._create_widgets()
        
        # تطبيق الثيم
        self._apply_theme()
        
        # توسيط النافذة
        self._center_window()
        
        # جعل النافذة مرئية
        self.window.deiconify()
        self.window.lift()
        self.window.attributes('-topmost', True)
        self.window.focus_force()
        
        # جعل النافذة دائماً في المقدمة
        self.window.transient(self.parent)
        
        # تطبيق grab_set بعد التأكد من ظهور النافذة
        self.window.after(100, self._apply_grab)
        
        # التركيز على حقل اسم المستخدم
        self.window.after(200, self._focus_username)
        
        # إزالة topmost بعد ثانية
        self.window.after(1000, lambda: self.window.attributes('-topmost', False))
        
        # بدء التأثيرات البصرية
        self._start_animations()
        
        # ربط مفتاح Enter
        self.window.bind('<Return>', lambda e: self._on_login())
    
    def _create_widgets(self):
        """إنشاء عناصر الواجهة"""
        # إطار رئيسي مع تدرج لوني
        self.main_frame = tk.Frame(self.window)
        self.main_frame.pack(fill='both', expand=True)
        
        # إطار الخلفية المتدرجة
        self.bg_canvas = tk.Canvas(self.main_frame, highlightthickness=0)
        self.bg_canvas.pack(fill='both', expand=True)
        
        # إطار المحتوى
        self.content_frame = tk.Frame(self.bg_canvas)
        self.bg_canvas.create_window(225, 325, window=self.content_frame, anchor='center')
        
        # شعار التطبيق
        self.logo_frame = tk.Frame(self.content_frame)
        self.logo_frame.pack(pady=(30, 20))
        
        self.logo_label = tk.Label(
            self.logo_frame,
            text="🍖",
            font=('Segoe UI Emoji', 48)
        )
        self.logo_label.pack()
        
        self.title_label = tk.Label(
            self.logo_frame,
            text="شاورما الدجاج",
            font=('Segoe UI', 24, 'bold')
        )
        self.title_label.pack(pady=(10, 0))
        
        self.subtitle_label = tk.Label(
            self.logo_frame,
            text="نظام نقطة البيع المتطور",
            font=('Segoe UI', 12)
        )
        self.subtitle_label.pack(pady=(5, 0))
        
        # بطاقة تسجيل الدخول
        self.login_card = ModernCard(self.content_frame, title="")
        self.login_card.pack(pady=30, padx=40, fill='x')
        
        # حقول الإدخال
        self.username_entry = ModernEntry(
            self.login_card.content_frame,
            label="اسم المستخدم",
            placeholder="أدخل اسم المستخدم"
        )
        self.username_entry.pack(fill='x', padx=20, pady=(20, 10))
        
        self.password_entry = ModernEntry(
            self.login_card.content_frame,
            label="كلمة المرور",
            placeholder="أدخل كلمة المرور"
        )
        self.password_entry.pack(fill='x', padx=20, pady=10)
        
        # تعيين نوع كلمة المرور
        self.password_entry.entry.configure(show="*")
        
        # خيار التذكر
        self.remember_frame = tk.Frame(self.login_card.content_frame)
        self.remember_frame.pack(fill='x', padx=20, pady=10)
        
        self.remember_var = tk.BooleanVar()
        self.remember_check = tk.Checkbutton(
            self.remember_frame,
            text="تذكر اسم المستخدم",
            variable=self.remember_var,
            font=('Segoe UI', 9),
            command=self._on_remember_change
        )
        self.remember_check.pack(anchor='w')
        
        # أزرار العمل
        self.buttons_frame = tk.Frame(self.login_card.content_frame)
        self.buttons_frame.pack(fill='x', padx=20, pady=(20, 30))
        
        self.login_button = ModernButton(
            self.buttons_frame,
            text="🔐 تسجيل الدخول",
            command=self._on_login,
            button_type="primary"
        )
        self.login_button.pack(fill='x', pady=(0, 10))
        
        self.guest_button = ModernButton(
            self.buttons_frame,
            text="👤 دخول كضيف",
            command=self._on_guest_login,
            button_type="default"
        )
        self.guest_button.pack(fill='x')
        
        # معلومات إضافية
        self.info_frame = tk.Frame(self.content_frame)
        self.info_frame.pack(pady=20)
        
        self.info_label = tk.Label(
            self.info_frame,
            text="💡 استخدم: admin / admin123 أو cashier / cashier123",
            font=('Segoe UI', 9)
        )
        self.info_label.pack()
        
        # تحميل اسم المستخدم المحفوظ
        self._load_saved_username()
    
    def _apply_theme(self):
        """تطبيق الثيم"""
        colors = theme_manager.get_current_theme()['colors']
        
        # الخلفية الرئيسية
        self.window.configure(bg=colors['background'])
        self.main_frame.configure(bg=colors['background'])
        self.bg_canvas.configure(bg=colors['background'])
        self.content_frame.configure(bg=colors['background'])
        
        # الشعار
        self.logo_frame.configure(bg=colors['background'])
        self.logo_label.configure(bg=colors['background'])
        self.title_label.configure(
            bg=colors['background'],
            fg=colors['text_primary']
        )
        self.subtitle_label.configure(
            bg=colors['background'],
            fg=colors['text_secondary']
        )
        
        # عناصر أخرى
        self.remember_frame.configure(bg=colors['card'])
        self.remember_check.configure(
            bg=colors['card'],
            fg=colors['text_secondary'],
            activebackground=colors['card'],
            activeforeground=colors['text_primary'],
            selectcolor=colors['card']
        )
        
        self.buttons_frame.configure(bg=colors['card'])
        self.info_frame.configure(bg=colors['background'])
        self.info_label.configure(
            bg=colors['background'],
            fg=colors['text_hint']
        )
        
        # تطبيق الثيم على المكونات الحديثة
        if hasattr(self, 'login_card'):
            self.login_card._apply_theme()
        if hasattr(self, 'username_entry'):
            self.username_entry._apply_theme()
        if hasattr(self, 'password_entry'):
            self.password_entry._apply_theme()
    
    def _center_window(self):
        """توسيط النافذة على الشاشة"""
        self.window.update_idletasks()
        
        # الحصول على أبعاد الشاشة
        screen_width = self.window.winfo_screenwidth()
        screen_height = self.window.winfo_screenheight()
        
        # حساب الموقع
        x = (screen_width - 450) // 2
        y = (screen_height - 650) // 2
        
        self.window.geometry(f"450x650+{x}+{y}")
    
    def _apply_grab(self):
        """تطبيق grab_set بعد التأكد من ظهور النافذة"""
        try:
            if self.window and self.window.winfo_exists():
                self.window.grab_set()
        except:
            pass
    
    def _focus_username(self):
        """التركيز على حقل اسم المستخدم"""
        try:
            if self.username_entry and self.window.winfo_exists():
                self.username_entry.entry.focus()
        except:
            pass
    
    def _start_animations(self):
        """بدء التأثيرات البصرية"""
        self._animate_gradient()
    
    def _animate_gradient(self):
        """تأثير التدرج اللوني المتحرك"""
        if not self.window or not self.window.winfo_exists():
            return
        
        try:
            colors = theme_manager.get_current_theme()['colors']
            
            # إنشاء تدرج لوني
            self.bg_canvas.delete("gradient")
            
            # رسم التدرج
            width = 450
            height = 650
            
            for i in range(height):
                # حساب اللون بناءً على الموقع والوقت
                ratio = i / height
                alpha = 0.1 + 0.1 * math.sin(self.animation_step * 0.02 + ratio * 2)
                
                # تحويل اللون الأساسي إلى RGB
                primary_color = colors['primary'].lstrip('#')
                r = int(primary_color[0:2], 16)
                g = int(primary_color[2:4], 16)
                b = int(primary_color[4:6], 16)
                
                # تطبيق الشفافية
                bg_color = colors['background'].lstrip('#')
                bg_r = int(bg_color[0:2], 16)
                bg_g = int(bg_color[2:4], 16)
                bg_b = int(bg_color[4:6], 16)
                
                final_r = int(bg_r + (r - bg_r) * alpha)
                final_g = int(bg_g + (g - bg_g) * alpha)
                final_b = int(bg_b + (b - bg_b) * alpha)
                
                color = f"#{final_r:02x}{final_g:02x}{final_b:02x}"
                
                self.bg_canvas.create_line(
                    0, i, width, i,
                    fill=color, tags="gradient"
                )
            
            self.animation_step += 1

            # تكرار التأثير
            self.window.after(100, self._animate_gradient)

        except:
            pass

    def _load_saved_username(self):
        """تحميل اسم المستخدم المحفوظ"""
        try:
            if hasattr(self.app, 'config'):
                saved_username = self.app.config.get('login.saved_username', '')
                if saved_username and self.username_entry:
                    self.username_entry.set(saved_username)
                    self.remember_var.set(True)
                    # التركيز على كلمة المرور
                    self.window.after(300, lambda: self.password_entry.entry.focus())
        except:
            pass

    def _on_remember_change(self):
        """معالج تغيير خيار التذكر"""
        if not self.remember_var.get():
            # حذف اسم المستخدم المحفوظ
            try:
                if hasattr(self.app, 'config'):
                    self.app.config.set('login.saved_username', '')
                    self.app.config.save_settings()
            except:
                pass

    def _on_login(self):
        """معالج تسجيل الدخول"""
        if self.is_logging_in:
            return

        username = self.username_entry.get().strip()
        password = self.password_entry.get().strip()

        if not username:
            show_notification(self.window, "يرجى إدخال اسم المستخدم", "warning")
            self.username_entry.entry.focus()
            return

        if not password:
            show_notification(self.window, "يرجى إدخال كلمة المرور", "warning")
            self.password_entry.entry.focus()
            return

        # بدء عملية تسجيل الدخول
        self.is_logging_in = True
        self._show_loading()

        # تشغيل التحقق في خيط منفصل
        threading.Thread(target=self._authenticate_user, args=(username, password), daemon=True).start()

    def _show_loading(self):
        """عرض حالة التحميل"""
        self.login_button.configure(
            text="🔄 جاري التحقق...",
            state='disabled'
        )

    def _hide_loading(self):
        """إخفاء حالة التحميل"""
        self.login_button.configure(
            text="🔐 تسجيل الدخول",
            state='normal'
        )
        self.is_logging_in = False

    def _authenticate_user(self, username: str, password: str):
        """التحقق من بيانات المستخدم"""
        try:
            # محاكاة وقت التحقق
            time.sleep(1)

            # التحقق من قاعدة البيانات
            user_data = self.app.db_manager.authenticate_user(username, password)

            # العودة للخيط الرئيسي
            self.window.after(0, lambda: self._handle_auth_result(user_data, username))

        except Exception as e:
            self.window.after(0, lambda: self._handle_auth_error(str(e)))

    def _handle_auth_result(self, user_data: Optional[Dict], username: str):
        """معالجة نتيجة التحقق"""
        self._hide_loading()

        if user_data:
            # نجح تسجيل الدخول
            show_notification(self.window, f"مرحباً {user_data['full_name']}", "success")

            # حفظ اسم المستخدم إذا كان مطلوباً
            if self.remember_var and self.remember_var.get():
                try:
                    if hasattr(self.app, 'config'):
                        self.app.config.set('login.saved_username', username)
                        self.app.config.save_settings()
                except:
                    pass

            # إغلاق نافذة تسجيل الدخول وفتح النافذة الرئيسية
            self.window.after(1000, lambda: self._complete_login(user_data))
        else:
            # فشل تسجيل الدخول
            show_notification(self.window, "اسم المستخدم أو كلمة المرور غير صحيحة", "error")
            if self.password_entry:
                self.password_entry.entry.delete(0, tk.END)
                self.password_entry.entry.focus()

    def _handle_auth_error(self, error_message: str):
        """معالجة خطأ التحقق"""
        self._hide_loading()
        show_notification(self.window, f"خطأ في التحقق: {error_message}", "error")

    def _complete_login(self, user_data: Dict):
        """إكمال عملية تسجيل الدخول"""
        try:
            # إخفاء نافذة تسجيل الدخول
            self.window.withdraw()

            # استدعاء دالة نجاح تسجيل الدخول في التطبيق الرئيسي
            self.app.on_login_success(user_data)

            # إغلاق النافذة
            self.destroy()

        except Exception as e:
            show_notification(self.window, f"خطأ في فتح التطبيق: {str(e)}", "error")

    def _on_guest_login(self):
        """تسجيل دخول كضيف"""
        guest_data = {
            'id': 0,
            'username': 'guest',
            'full_name': 'ضيف',
            'role': 'guest',
            'phone': '',
            'email': '',
            'is_active': 1
        }

        show_notification(self.window, "تم تسجيل الدخول كضيف", "info")
        self.window.after(1000, lambda: self._complete_login(guest_data))

    def _on_closing(self):
        """معالج إغلاق النافذة"""
        if messagebox.askyesno("إغلاق التطبيق", "هل تريد إغلاق التطبيق؟"):
            self.app.root.quit()

    def destroy(self):
        """تدمير النافذة"""
        if self.window:
            try:
                self.window.grab_release()
                self.window.destroy()
            except:
                pass
            self.window = None
