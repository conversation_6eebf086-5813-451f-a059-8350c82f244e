#!/usr/bin/env python3
# -*- coding: utf-8 -*-
"""
النافذة الرئيسية الحديثة والمتطورة
Modern and Advanced Main Window
"""

import tkinter as tk
from tkinter import ttk, messagebox
from typing import Dict, Any, Optional, List
from .theme_manager import theme_manager
from .modern_components import ModernButton, ModernCard, show_notification
import datetime

class ModernMainWindow:
    """النافذة الرئيسية الحديثة"""
    
    def __init__(self, parent: tk.Tk, app, user_data: Dict[str, Any]):
        self.parent = parent
        self.app = app
        self.user_data = user_data
        self.current_order = []
        self.total_amount = 0.0
        
        # إعداد النافذة الرئيسية
        self._setup_main_window()
        
        # إنشاء الواجهة
        self._create_widgets()
        
        # تطبيق الثيم
        self._apply_theme()
        
        # تحديث المعلومات
        self._update_user_info()
        self._update_datetime()
        
        # بدء التحديثات الدورية
        self._start_periodic_updates()
    
    def _setup_main_window(self):
        """إعداد النافذة الرئيسية"""
        self.parent.title(f"🍖 شاورما الدجاج - نظام نقطة البيع | {self.user_data['full_name']}")
        self.parent.geometry("1400x900")
        self.parent.state('zoomed')  # ملء الشاشة
        
        # منع إغلاق النافذة مباشرة
        self.parent.protocol("WM_DELETE_WINDOW", self._on_closing)
    
    def _create_widgets(self):
        """إنشاء عناصر الواجهة"""
        # الشريط العلوي
        self._create_header()
        
        # المحتوى الرئيسي
        self._create_main_content()
        
        # الشريط السفلي
        self._create_footer()
    
    def _create_header(self):
        """إنشاء الشريط العلوي"""
        self.header_frame = tk.Frame(self.parent, height=80)
        self.header_frame.pack(fill='x', side='top')
        self.header_frame.pack_propagate(False)
        
        # الجانب الأيسر - معلومات المستخدم
        self.user_info_frame = tk.Frame(self.header_frame)
        self.user_info_frame.pack(side='left', padx=20, pady=10)
        
        self.welcome_label = tk.Label(
            self.user_info_frame,
            text=f"مرحباً، {self.user_data['full_name']} 👋",
            font=('Segoe UI', 14, 'bold')
        )
        self.welcome_label.pack(anchor='w')
        
        self.role_label = tk.Label(
            self.user_info_frame,
            text=f"الصلاحية: {self._get_role_name(self.user_data['role'])}",
            font=('Segoe UI', 10)
        )
        self.role_label.pack(anchor='w')
        
        # الوسط - شعار وعنوان
        self.logo_frame = tk.Frame(self.header_frame)
        self.logo_frame.pack(expand=True)
        
        self.logo_title = tk.Label(
            self.logo_frame,
            text="🍖 شاورما الدجاج",
            font=('Segoe UI', 18, 'bold')
        )
        self.logo_title.pack(pady=10)
        
        self.subtitle = tk.Label(
            self.logo_frame,
            text="نظام نقطة البيع المتطور",
            font=('Segoe UI', 10)
        )
        self.subtitle.pack()
        
        # الجانب الأيمن - أدوات التحكم
        self.controls_frame = tk.Frame(self.header_frame)
        self.controls_frame.pack(side='right', padx=20, pady=10)
        
        # التاريخ والوقت
        self.datetime_label = tk.Label(
            self.controls_frame,
            font=('Segoe UI', 10)
        )
        self.datetime_label.pack(anchor='e')
        
        # أزرار التحكم
        self.control_buttons_frame = tk.Frame(self.controls_frame)
        self.control_buttons_frame.pack(anchor='e', pady=(5, 0))
        
        # زر تغيير الثيم
        self.theme_button = ModernButton(
            self.control_buttons_frame,
            text="🎨",
            command=self._show_theme_selector,
            button_type="default"
        )
        self.theme_button.pack(side='right', padx=(5, 0))
        
        # زر الإحصائيات المتقدمة
        self.advanced_stats_button = ModernButton(
            self.control_buttons_frame,
            text="📈",
            command=self._show_advanced_stats,
            button_type="default"
        )
        self.advanced_stats_button.pack(side='right', padx=(5, 0))

        # زر إحصائيات الأداء
        self.performance_button = ModernButton(
            self.control_buttons_frame,
            text="📊",
            command=self._show_performance,
            button_type="default"
        )
        self.performance_button.pack(side='right', padx=(5, 0))

        # زر الإعدادات
        self.settings_button = ModernButton(
            self.control_buttons_frame,
            text="⚙️",
            command=self._show_settings,
            button_type="default"
        )
        self.settings_button.pack(side='right', padx=(5, 0))
        
        # زر تسجيل الخروج
        self.logout_button = ModernButton(
            self.control_buttons_frame,
            text="🚪 خروج",
            command=self._logout,
            button_type="danger"
        )
        self.logout_button.pack(side='right')
    
    def _create_main_content(self):
        """إنشاء المحتوى الرئيسي"""
        self.main_frame = tk.Frame(self.parent)
        self.main_frame.pack(fill='both', expand=True, padx=10, pady=5)
        
        # الشريط الجانبي الأيسر - قائمة المنتجات
        self.sidebar_frame = tk.Frame(self.main_frame, width=350)
        self.sidebar_frame.pack(side='left', fill='y', padx=(0, 10))
        self.sidebar_frame.pack_propagate(False)
        
        # بطاقة المنتجات
        self.products_card = ModernCard(self.sidebar_frame, title="📋 قائمة المنتجات")
        self.products_card.pack(fill='both', expand=True)
        
        # شريط البحث
        self.search_frame = tk.Frame(self.products_card.content_frame)
        self.search_frame.pack(fill='x', padx=15, pady=10)
        
        self.search_entry = tk.Entry(
            self.search_frame,
            placeholder_text="🔍 البحث في المنتجات...",
            font=('Segoe UI', 10)
        )
        self.search_entry.pack(fill='x')
        
        # قائمة المنتجات
        self.products_frame = tk.Frame(self.products_card.content_frame)
        self.products_frame.pack(fill='both', expand=True, padx=15, pady=(0, 15))
        
        # إنشاء scrollbar للمنتجات
        self.products_canvas = tk.Canvas(self.products_frame)
        self.products_scrollbar = ttk.Scrollbar(
            self.products_frame, 
            orient="vertical", 
            command=self.products_canvas.yview
        )
        self.products_scrollable_frame = tk.Frame(self.products_canvas)
        
        self.products_scrollable_frame.bind(
            "<Configure>",
            lambda e: self.products_canvas.configure(scrollregion=self.products_canvas.bbox("all"))
        )
        
        self.products_canvas.create_window((0, 0), window=self.products_scrollable_frame, anchor="nw")
        self.products_canvas.configure(yscrollcommand=self.products_scrollbar.set)
        
        self.products_canvas.pack(side="left", fill="both", expand=True)
        self.products_scrollbar.pack(side="right", fill="y")
        
        # المنطقة الوسطى - الطلب الحالي
        self.order_frame = tk.Frame(self.main_frame, width=400)
        self.order_frame.pack(side='left', fill='y', padx=(0, 10))
        self.order_frame.pack_propagate(False)
        
        # بطاقة الطلب الحالي
        self.order_card = ModernCard(self.order_frame, title="🛒 الطلب الحالي")
        self.order_card.pack(fill='both', expand=True)
        
        # قائمة عناصر الطلب
        self.order_items_frame = tk.Frame(self.order_card.content_frame)
        self.order_items_frame.pack(fill='both', expand=True, padx=15, pady=10)
        
        # إجمالي الطلب
        self.total_frame = tk.Frame(self.order_card.content_frame)
        self.total_frame.pack(fill='x', padx=15, pady=(0, 15))
        
        self.total_label = tk.Label(
            self.total_frame,
            text="الإجمالي: 0.00 ريال",
            font=('Segoe UI', 16, 'bold')
        )
        self.total_label.pack()
        
        # أزرار العمل
        self.action_buttons_frame = tk.Frame(self.order_card.content_frame)
        self.action_buttons_frame.pack(fill='x', padx=15, pady=(0, 15))
        
        self.checkout_button = ModernButton(
            self.action_buttons_frame,
            text="💳 إتمام الطلب",
            command=self._checkout_order,
            button_type="success"
        )
        self.checkout_button.pack(fill='x', pady=(0, 5))
        
        self.clear_button = ModernButton(
            self.action_buttons_frame,
            text="🗑️ مسح الطلب",
            command=self._clear_order,
            button_type="danger"
        )
        self.clear_button.pack(fill='x')
        
        # الجانب الأيمن - الإحصائيات والتقارير
        self.stats_frame = tk.Frame(self.main_frame)
        self.stats_frame.pack(side='right', fill='both', expand=True)
        
        # بطاقة الإحصائيات
        self.stats_card = ModernCard(self.stats_frame, title="📊 إحصائيات اليوم")
        self.stats_card.pack(fill='both', expand=True)
        
        # محتوى الإحصائيات
        self.stats_content_frame = tk.Frame(self.stats_card.content_frame)
        self.stats_content_frame.pack(fill='both', expand=True, padx=15, pady=15)
        
        # تحميل المنتجات
        self._load_products()
        
        # تحديث الإحصائيات
        self._update_stats()
    
    def _create_footer(self):
        """إنشاء الشريط السفلي"""
        self.footer_frame = tk.Frame(self.parent, height=40)
        self.footer_frame.pack(fill='x', side='bottom')
        self.footer_frame.pack_propagate(False)
        
        # معلومات النظام
        self.system_info_label = tk.Label(
            self.footer_frame,
            text="نظام نقطة البيع - شاورما الدجاج © 2024",
            font=('Segoe UI', 9)
        )
        self.system_info_label.pack(side='left', padx=20, pady=10)
        
        # حالة الاتصال
        self.connection_status_label = tk.Label(
            self.footer_frame,
            text="🟢 متصل",
            font=('Segoe UI', 9)
        )
        self.connection_status_label.pack(side='right', padx=20, pady=10)

    def _apply_theme(self):
        """تطبيق الثيم على جميع العناصر"""
        colors = theme_manager.get_current_theme()['colors']

        # النافذة الرئيسية
        self.parent.configure(bg=colors['background'])

        # الشريط العلوي
        self.header_frame.configure(bg=colors['surface'])
        self.user_info_frame.configure(bg=colors['surface'])
        self.welcome_label.configure(bg=colors['surface'], fg=colors['text_primary'])
        self.role_label.configure(bg=colors['surface'], fg=colors['text_secondary'])

        self.logo_frame.configure(bg=colors['surface'])
        self.logo_title.configure(bg=colors['surface'], fg=colors['primary'])
        self.subtitle.configure(bg=colors['surface'], fg=colors['text_secondary'])

        self.controls_frame.configure(bg=colors['surface'])
        self.datetime_label.configure(bg=colors['surface'], fg=colors['text_secondary'])
        self.control_buttons_frame.configure(bg=colors['surface'])

        # المحتوى الرئيسي
        self.main_frame.configure(bg=colors['background'])
        self.sidebar_frame.configure(bg=colors['background'])
        self.order_frame.configure(bg=colors['background'])
        self.stats_frame.configure(bg=colors['background'])

        # عناصر أخرى
        self.search_frame.configure(bg=colors['card'])
        self.products_frame.configure(bg=colors['card'])
        self.products_canvas.configure(bg=colors['card'])
        self.products_scrollable_frame.configure(bg=colors['card'])

        self.order_items_frame.configure(bg=colors['card'])
        self.total_frame.configure(bg=colors['card'])
        self.total_label.configure(bg=colors['card'], fg=colors['success'])
        self.action_buttons_frame.configure(bg=colors['card'])

        self.stats_content_frame.configure(bg=colors['card'])

        # الشريط السفلي
        self.footer_frame.configure(bg=colors['surface'])
        self.system_info_label.configure(bg=colors['surface'], fg=colors['text_hint'])
        self.connection_status_label.configure(bg=colors['surface'], fg=colors['success'])

    def _get_role_name(self, role: str) -> str:
        """الحصول على اسم الصلاحية بالعربية"""
        roles = {
            'admin': 'مدير النظام',
            'manager': 'مدير',
            'cashier': 'كاشير',
            'guest': 'ضيف'
        }
        return roles.get(role, role)

    def _update_user_info(self):
        """تحديث معلومات المستخدم"""
        self.welcome_label.configure(text=f"مرحباً، {self.user_data['full_name']} 👋")
        self.role_label.configure(text=f"الصلاحية: {self._get_role_name(self.user_data['role'])}")

    def _update_datetime(self):
        """تحديث التاريخ والوقت"""
        now = datetime.datetime.now()
        date_str = now.strftime("%Y/%m/%d")
        time_str = now.strftime("%H:%M:%S")
        self.datetime_label.configure(text=f"📅 {date_str}\n🕐 {time_str}")

    def _start_periodic_updates(self):
        """بدء التحديثات الدورية"""
        self._update_datetime()
        self.parent.after(1000, self._start_periodic_updates)

    def _load_products(self):
        """تحميل المنتجات"""
        try:
            # مسح المنتجات الحالية
            for widget in self.products_scrollable_frame.winfo_children():
                widget.destroy()

            # الحصول على المنتجات من قاعدة البيانات (محسن)
            products = self.app.db_manager.get_all_products_optimized()

            for product in products:
                self._create_product_item(product)

        except Exception as e:
            show_notification(self.parent, f"خطأ في تحميل المنتجات: {str(e)}", "error")

    def _create_product_item(self, product: Dict[str, Any]):
        """إنشاء عنصر منتج"""
        item_frame = tk.Frame(self.products_scrollable_frame, relief='solid', borderwidth=1)
        item_frame.pack(fill='x', pady=2)

        # معلومات المنتج
        info_frame = tk.Frame(item_frame)
        info_frame.pack(side='left', fill='both', expand=True, padx=10, pady=5)

        name_label = tk.Label(
            info_frame,
            text=product['name'],
            font=('Segoe UI', 11, 'bold'),
            anchor='w'
        )
        name_label.pack(fill='x')

        price_label = tk.Label(
            info_frame,
            text=f"{product['price']:.2f} ريال",
            font=('Segoe UI', 10),
            anchor='w'
        )
        price_label.pack(fill='x')

        # زر الإضافة
        add_button = ModernButton(
            item_frame,
            text="➕",
            command=lambda p=product: self._add_to_order(p),
            button_type="primary"
        )
        add_button.pack(side='right', padx=5, pady=5)

        # تطبيق الثيم
        colors = theme_manager.get_current_theme()['colors']
        item_frame.configure(bg=colors['card'], highlightbackground=colors['border'])
        info_frame.configure(bg=colors['card'])
        name_label.configure(bg=colors['card'], fg=colors['text_primary'])
        price_label.configure(bg=colors['card'], fg=colors['text_secondary'])

    def _add_to_order(self, product: Dict[str, Any]):
        """إضافة منتج للطلب"""
        # البحث عن المنتج في الطلب الحالي
        for item in self.current_order:
            if item['product_id'] == product['id']:
                item['quantity'] += 1
                item['total'] = item['quantity'] * item['price']
                break
        else:
            # إضافة منتج جديد
            self.current_order.append({
                'product_id': product['id'],
                'name': product['name'],
                'price': product['price'],
                'quantity': 1,
                'total': product['price']
            })

        self._update_order_display()
        show_notification(self.parent, f"تم إضافة {product['name']}", "success", 1500)

    def _update_order_display(self):
        """تحديث عرض الطلب الحالي"""
        # مسح العرض الحالي
        for widget in self.order_items_frame.winfo_children():
            widget.destroy()

        # حساب الإجمالي
        self.total_amount = 0.0

        for item in self.current_order:
            self.total_amount += item['total']

            # إنشاء عنصر الطلب
            item_frame = tk.Frame(self.order_items_frame, relief='solid', borderwidth=1)
            item_frame.pack(fill='x', pady=2)

            # معلومات العنصر
            info_frame = tk.Frame(item_frame)
            info_frame.pack(side='left', fill='both', expand=True, padx=10, pady=5)

            name_label = tk.Label(
                info_frame,
                text=item['name'],
                font=('Segoe UI', 10, 'bold'),
                anchor='w'
            )
            name_label.pack(fill='x')

            details_label = tk.Label(
                info_frame,
                text=f"{item['quantity']} × {item['price']:.2f} = {item['total']:.2f} ريال",
                font=('Segoe UI', 9),
                anchor='w'
            )
            details_label.pack(fill='x')

            # أزرار التحكم
            controls_frame = tk.Frame(item_frame)
            controls_frame.pack(side='right', padx=5, pady=5)

            # زر الزيادة
            plus_button = ModernButton(
                controls_frame,
                text="➕",
                command=lambda i=item: self._increase_quantity(i),
                button_type="success"
            )
            plus_button.pack(side='top', pady=1)

            # زر النقصان
            minus_button = ModernButton(
                controls_frame,
                text="➖",
                command=lambda i=item: self._decrease_quantity(i),
                button_type="warning"
            )
            minus_button.pack(side='top', pady=1)

            # زر الحذف
            remove_button = ModernButton(
                controls_frame,
                text="🗑️",
                command=lambda i=item: self._remove_from_order(i),
                button_type="danger"
            )
            remove_button.pack(side='top', pady=1)

            # تطبيق الثيم
            colors = theme_manager.get_current_theme()['colors']
            item_frame.configure(bg=colors['card'], highlightbackground=colors['border'])
            info_frame.configure(bg=colors['card'])
            controls_frame.configure(bg=colors['card'])
            name_label.configure(bg=colors['card'], fg=colors['text_primary'])
            details_label.configure(bg=colors['card'], fg=colors['text_secondary'])

        # تحديث الإجمالي
        self.total_label.configure(text=f"الإجمالي: {self.total_amount:.2f} ريال")

        # تفعيل/تعطيل أزرار العمل
        if self.current_order:
            self.checkout_button.configure(state='normal')
            self.clear_button.configure(state='normal')
        else:
            self.checkout_button.configure(state='disabled')
            self.clear_button.configure(state='disabled')

    def _increase_quantity(self, item: Dict[str, Any]):
        """زيادة كمية المنتج"""
        item['quantity'] += 1
        item['total'] = item['quantity'] * item['price']
        self._update_order_display()

    def _decrease_quantity(self, item: Dict[str, Any]):
        """تقليل كمية المنتج"""
        if item['quantity'] > 1:
            item['quantity'] -= 1
            item['total'] = item['quantity'] * item['price']
            self._update_order_display()
        else:
            self._remove_from_order(item)

    def _remove_from_order(self, item: Dict[str, Any]):
        """حذف منتج من الطلب"""
        self.current_order.remove(item)
        self._update_order_display()
        show_notification(self.parent, f"تم حذف {item['name']}", "info", 1500)

    def _clear_order(self):
        """مسح الطلب بالكامل"""
        if messagebox.askyesno("تأكيد المسح", "هل تريد مسح الطلب بالكامل؟"):
            self.current_order.clear()
            self._update_order_display()
            show_notification(self.parent, "تم مسح الطلب", "info")

    def _checkout_order(self):
        """إتمام الطلب"""
        if not self.current_order:
            show_notification(self.parent, "الطلب فارغ", "warning")
            return

        try:
            # إنشاء الطلب في قاعدة البيانات (محسن)
            order_id = self.app.db_manager.create_order_optimized(
                user_id=self.user_data['id'],
                items=self.current_order,
                total=self.total_amount
            )

            # عرض رسالة نجاح
            show_notification(
                self.parent,
                f"تم إتمام الطلب بنجاح\nرقم الطلب: {order_id}",
                "success"
            )

            # مسح الطلب الحالي
            self.current_order.clear()
            self._update_order_display()

            # تحديث الإحصائيات
            self._update_stats()

        except Exception as e:
            show_notification(self.parent, f"خطأ في إتمام الطلب: {str(e)}", "error")

    def _update_stats(self):
        """تحديث الإحصائيات"""
        try:
            # مسح الإحصائيات الحالية
            for widget in self.stats_content_frame.winfo_children():
                widget.destroy()

            # الحصول على إحصائيات اليوم (محسن)
            today_stats = self.app.db_manager.get_today_stats_optimized()

            # عرض الإحصائيات
            stats_data = [
                ("💰 إجمالي المبيعات", f"{today_stats.get('total_sales', 0):.2f} ريال"),
                ("📦 عدد الطلبات", str(today_stats.get('total_orders', 0))),
                ("🛒 متوسط الطلب", f"{today_stats.get('avg_order', 0):.2f} ريال"),
                ("⭐ أكثر المنتجات مبيعاً", today_stats.get('top_product', 'لا يوجد'))
            ]

            for i, (label, value) in enumerate(stats_data):
                stat_frame = tk.Frame(self.stats_content_frame)
                stat_frame.pack(fill='x', pady=5)

                label_widget = tk.Label(
                    stat_frame,
                    text=label,
                    font=('Segoe UI', 10),
                    anchor='w'
                )
                label_widget.pack(side='left')

                value_widget = tk.Label(
                    stat_frame,
                    text=value,
                    font=('Segoe UI', 10, 'bold'),
                    anchor='e'
                )
                value_widget.pack(side='right')

                # تطبيق الثيم
                colors = theme_manager.get_current_theme()['colors']
                stat_frame.configure(bg=colors['card'])
                label_widget.configure(bg=colors['card'], fg=colors['text_secondary'])
                value_widget.configure(bg=colors['card'], fg=colors['text_primary'])

        except Exception as e:
            show_notification(self.parent, f"خطأ في تحديث الإحصائيات: {str(e)}", "error")

    def _show_theme_selector(self):
        """عرض نافذة اختيار الثيم"""
        theme_window = tk.Toplevel(self.parent)
        theme_window.title("اختيار الثيم")
        theme_window.geometry("300x200")
        theme_window.resizable(False, False)

        # توسيط النافذة
        theme_window.transient(self.parent)
        theme_window.grab_set()

        # عنوان
        title_label = tk.Label(
            theme_window,
            text="🎨 اختر الثيم المفضل",
            font=('Segoe UI', 14, 'bold')
        )
        title_label.pack(pady=20)

        # أزرار الثيمات
        themes = theme_manager.get_available_themes()

        for theme_key, theme_name in themes.items():
            button = ModernButton(
                theme_window,
                text=f"{theme_name} {'✓' if theme_key == theme_manager.current_theme else ''}",
                command=lambda t=theme_key: self._change_theme(t, theme_window),
                button_type="primary" if theme_key == theme_manager.current_theme else "default"
            )
            button.pack(pady=5, padx=20, fill='x')

        # تطبيق الثيم على النافذة
        colors = theme_manager.get_current_theme()['colors']
        theme_window.configure(bg=colors['background'])
        title_label.configure(bg=colors['background'], fg=colors['text_primary'])

    def _change_theme(self, theme_name: str, window: tk.Toplevel):
        """تغيير الثيم"""
        theme_manager.set_theme(theme_name)
        self._apply_theme()

        # إعادة تطبيق الثيم على جميع المكونات
        self._refresh_all_components()

        show_notification(self.parent, f"تم تغيير الثيم إلى {theme_manager.get_current_theme()['name']}", "success")
        window.destroy()

    def _refresh_all_components(self):
        """إعادة تطبيق الثيم على جميع المكونات"""
        # إعادة تحميل المنتجات
        self._load_products()

        # إعادة تحديث الطلب
        self._update_order_display()

        # إعادة تحديث الإحصائيات
        self._update_stats()

    def _show_advanced_stats(self):
        """عرض نافذة الإحصائيات المتقدمة"""
        try:
            from .advanced_stats_window import AdvancedStatsWindow
            advanced_stats_window = AdvancedStatsWindow(self.parent, self.app)
            advanced_stats_window.show()
        except Exception as e:
            show_notification(self.parent, f"خطأ في فتح نافذة الإحصائيات المتقدمة: {str(e)}", "error")

    def _show_performance(self):
        """عرض نافذة إحصائيات الأداء"""
        try:
            from .performance_window import PerformanceWindow
            performance_window = PerformanceWindow(self.parent, self.app)
            performance_window.show()
        except Exception as e:
            show_notification(self.parent, f"خطأ في فتح نافذة الأداء: {str(e)}", "error")

    def _show_settings(self):
        """عرض نافذة الإعدادات"""
        show_notification(self.parent, "نافذة الإعدادات قيد التطوير", "info")

    def _logout(self):
        """تسجيل الخروج"""
        if messagebox.askyesno("تسجيل الخروج", "هل تريد تسجيل الخروج؟"):
            # إخفاء النافذة الرئيسية
            self.parent.withdraw()

            # إعادة عرض نافذة تسجيل الدخول
            self.app.show_login()

    def _on_closing(self):
        """معالج إغلاق النافذة"""
        if messagebox.askyesno("إغلاق التطبيق", "هل تريد إغلاق التطبيق؟"):
            self.parent.quit()
