#!/usr/bin/env python3
# -*- coding: utf-8 -*-
"""
نافذة الإعدادات - نظام نقطة البيع
Settings Dialog for POS System
"""

import tkinter as tk
from tkinter import ttk, messagebox
import json

class SettingsDialog:
    """نافذة إعدادات النظام"""
    
    def __init__(self, parent, config, app_instance):
        """تهيئة نافذة الإعدادات"""
        self.parent = parent
        self.config = config
        self.app = app_instance
        
        self.dialog = None
        self.notebook = None
        
        # نسخ مؤقتة من الإعدادات
        self.temp_settings = {}
        self._load_temp_settings()
    
    def show(self):
        """عرض نافذة الإعدادات"""
        self.dialog = tk.Toplevel(self.parent)
        self.dialog.title("إعدادات النظام")
        self.dialog.geometry("600x500")
        self.dialog.resizable(True, True)
        
        # توسيط النافذة
        self._center_dialog()
        
        # جعل النافذة مودالية
        self.dialog.transient(self.parent)
        self.dialog.grab_set()
        
        # إنشاء الواجهة
        self._create_widgets()
    
    def _center_dialog(self):
        """توسيط النافذة"""
        self.dialog.update_idletasks()
        
        parent_x = self.parent.winfo_rootx()
        parent_y = self.parent.winfo_rooty()
        parent_width = self.parent.winfo_width()
        parent_height = self.parent.winfo_height()
        
        x = parent_x + (parent_width - 600) // 2
        y = parent_y + (parent_height - 500) // 2
        
        self.dialog.geometry(f"600x500+{x}+{y}")
    
    def _load_temp_settings(self):
        """تحميل الإعدادات المؤقتة"""
        try:
            with open('config.json', 'r', encoding='utf-8') as f:
                self.temp_settings = json.load(f)
        except FileNotFoundError:
            # إنشاء إعدادات افتراضية
            self.temp_settings = {
                'restaurant': {
                    'name': 'مطعم شاورما الدجاج',
                    'address': 'شارع الملك فهد، الرياض',
                    'phone': '+966 11 234 5678',
                    'vat_number': '300123456789003'
                },
                'business': {
                    'tax_rate': 0.15,
                    'currency': 'ر.س'
                },
                'printer': {
                    'enabled': True,
                    'printer_name': 'default',
                    'paper_width': 80,
                    'receipt_footer': 'شكراً لزيارتكم'
                }
            }
    
    def _create_widgets(self):
        """إنشاء عناصر الواجهة"""
        # الإطار الرئيسي
        main_frame = ttk.Frame(self.dialog, padding="10")
        main_frame.pack(fill='both', expand=True)
        
        # العنوان
        title_label = ttk.Label(
            main_frame,
            text="⚙️ إعدادات النظام",
            font=("Tahoma", 16, "bold"),
            foreground="#2C3E50"
        )
        title_label.pack(pady=(0, 20))
        
        # دفتر الملاحظات للتبويبات
        self.notebook = ttk.Notebook(main_frame)
        self.notebook.pack(fill='both', expand=True, pady=(0, 20))
        
        # إنشاء التبويبات
        self._create_restaurant_tab()
        self._create_business_tab()
        self._create_printer_tab()
        
        # أزرار التحكم
        self._create_control_buttons(main_frame)
    
    def _create_restaurant_tab(self):
        """تبويب إعدادات المطعم"""
        frame = ttk.Frame(self.notebook)
        self.notebook.add(frame, text="المطعم")
        
        # معلومات المطعم الأساسية
        info_frame = ttk.LabelFrame(frame, text="المعلومات الأساسية", padding="15")
        info_frame.pack(fill='x', pady=10, padx=10)
        
        # اسم المطعم
        name_frame = ttk.Frame(info_frame)
        name_frame.pack(fill='x', pady=5)
        
        ttk.Label(name_frame, text="اسم المطعم:", font=("Tahoma", 10, "bold")).pack(anchor='e', pady=2)
        self.restaurant_name_var = tk.StringVar(value=self.temp_settings['restaurant']['name'])
        ttk.Entry(name_frame, textvariable=self.restaurant_name_var, width=50).pack(fill='x', pady=2)
        
        # العنوان
        address_frame = ttk.Frame(info_frame)
        address_frame.pack(fill='x', pady=5)
        
        ttk.Label(address_frame, text="العنوان:", font=("Tahoma", 10, "bold")).pack(anchor='e', pady=2)
        self.restaurant_address_var = tk.StringVar(value=self.temp_settings['restaurant']['address'])
        ttk.Entry(address_frame, textvariable=self.restaurant_address_var, width=50).pack(fill='x', pady=2)
        
        # الهاتف
        phone_frame = ttk.Frame(info_frame)
        phone_frame.pack(fill='x', pady=5)
        
        ttk.Label(phone_frame, text="رقم الهاتف:", font=("Tahoma", 10, "bold")).pack(anchor='e', pady=2)
        self.restaurant_phone_var = tk.StringVar(value=self.temp_settings['restaurant']['phone'])
        ttk.Entry(phone_frame, textvariable=self.restaurant_phone_var, width=30).pack(anchor='e', pady=2)
        
        # الرقم الضريبي
        vat_frame = ttk.Frame(info_frame)
        vat_frame.pack(fill='x', pady=5)
        
        ttk.Label(vat_frame, text="الرقم الضريبي:", font=("Tahoma", 10, "bold")).pack(anchor='e', pady=2)
        self.vat_number_var = tk.StringVar(value=self.temp_settings['restaurant']['vat_number'])
        ttk.Entry(vat_frame, textvariable=self.vat_number_var, width=30).pack(anchor='e', pady=2)
    
    def _create_business_tab(self):
        """تبويب الإعدادات التجارية"""
        frame = ttk.Frame(self.notebook)
        self.notebook.add(frame, text="التجارة")
        
        # إعدادات الضرائب والعملة
        tax_frame = ttk.LabelFrame(frame, text="الضرائب والعملة", padding="15")
        tax_frame.pack(fill='x', pady=10, padx=10)
        
        # معدل الضريبة
        tax_rate_frame = ttk.Frame(tax_frame)
        tax_rate_frame.pack(fill='x', pady=5)
        
        ttk.Label(tax_rate_frame, text="معدل ضريبة القيمة المضافة (%):", font=("Tahoma", 10, "bold")).pack(side='right', padx=(0, 10))
        self.tax_rate_var = tk.DoubleVar(value=self.temp_settings['business']['tax_rate'] * 100)
        ttk.Entry(tax_rate_frame, textvariable=self.tax_rate_var, width=10).pack(side='left')
        
        # العملة
        currency_frame = ttk.Frame(tax_frame)
        currency_frame.pack(fill='x', pady=5)
        
        ttk.Label(currency_frame, text="العملة:", font=("Tahoma", 10, "bold")).pack(side='right', padx=(0, 10))
        self.currency_var = tk.StringVar(value=self.temp_settings['business']['currency'])
        ttk.Entry(currency_frame, textvariable=self.currency_var, width=10).pack(side='left')
    
    def _create_printer_tab(self):
        """تبويب إعدادات الطابعة"""
        frame = ttk.Frame(self.notebook)
        self.notebook.add(frame, text="الطابعة")
        
        # إعدادات الطابعة
        printer_frame = ttk.LabelFrame(frame, text="إعدادات الطابعة", padding="15")
        printer_frame.pack(fill='x', pady=10, padx=10)
        
        # تفعيل الطابعة
        enable_frame = ttk.Frame(printer_frame)
        enable_frame.pack(fill='x', pady=5)
        
        self.printer_enabled_var = tk.BooleanVar(value=self.temp_settings['printer']['enabled'])
        ttk.Checkbutton(
            enable_frame,
            text="تفعيل الطباعة",
            variable=self.printer_enabled_var
        ).pack(side='right')
        
        # اسم الطابعة
        name_frame = ttk.Frame(printer_frame)
        name_frame.pack(fill='x', pady=5)
        
        ttk.Label(name_frame, text="اسم الطابعة:", font=("Tahoma", 10, "bold")).pack(side='right', padx=(0, 10))
        self.printer_name_var = tk.StringVar(value=self.temp_settings['printer']['printer_name'])
        ttk.Entry(name_frame, textvariable=self.printer_name_var, width=30).pack(side='left')
        
        # عرض الورق
        width_frame = ttk.Frame(printer_frame)
        width_frame.pack(fill='x', pady=5)
        
        ttk.Label(width_frame, text="عرض الورق (مم):", font=("Tahoma", 10, "bold")).pack(side='right', padx=(0, 10))
        self.paper_width_var = tk.IntVar(value=self.temp_settings['printer']['paper_width'])
        width_combo = ttk.Combobox(
            width_frame,
            textvariable=self.paper_width_var,
            values=[58, 80],
            state="readonly",
            width=10
        )
        width_combo.pack(side='left')
        
        # ذيل الفاتورة
        footer_frame = ttk.Frame(printer_frame)
        footer_frame.pack(fill='x', pady=10)
        
        ttk.Label(footer_frame, text="ذيل الفاتورة:", font=("Tahoma", 10, "bold")).pack(anchor='e', pady=2)
        self.receipt_footer_var = tk.StringVar(value=self.temp_settings['printer']['receipt_footer'])
        ttk.Entry(footer_frame, textvariable=self.receipt_footer_var, width=50).pack(fill='x', pady=2)
    
    def _create_control_buttons(self, parent):
        """إنشاء أزرار التحكم"""
        buttons_frame = ttk.Frame(parent)
        buttons_frame.pack(fill='x')
        
        # زر الإلغاء
        ttk.Button(
            buttons_frame,
            text="إلغاء",
            command=self._on_cancel,
            width=15
        ).pack(side='left')
        
        # زر الحفظ
        ttk.Button(
            buttons_frame,
            text="حفظ",
            command=self._on_save,
            width=15
        ).pack(side='right', padx=5)
    
    def _save_settings(self):
        """حفظ الإعدادات"""
        try:
            # تحديث الإعدادات المؤقتة
            self.temp_settings['restaurant']['name'] = self.restaurant_name_var.get()
            self.temp_settings['restaurant']['address'] = self.restaurant_address_var.get()
            self.temp_settings['restaurant']['phone'] = self.restaurant_phone_var.get()
            self.temp_settings['restaurant']['vat_number'] = self.vat_number_var.get()
            
            self.temp_settings['business']['tax_rate'] = self.tax_rate_var.get() / 100
            self.temp_settings['business']['currency'] = self.currency_var.get()
            
            self.temp_settings['printer']['enabled'] = self.printer_enabled_var.get()
            self.temp_settings['printer']['printer_name'] = self.printer_name_var.get()
            self.temp_settings['printer']['paper_width'] = self.paper_width_var.get()
            self.temp_settings['printer']['receipt_footer'] = self.receipt_footer_var.get()
            
            # كتابة الإعدادات إلى الملف
            with open('config.json', 'w', encoding='utf-8') as f:
                json.dump(self.temp_settings, f, ensure_ascii=False, indent=4)
            
            return True
            
        except Exception as e:
            messagebox.showerror("خطأ", f"خطأ في حفظ الإعدادات: {str(e)}")
            return False
    
    def _on_save(self):
        """حفظ الإعدادات"""
        if self._save_settings():
            messagebox.showinfo("نجح", "تم حفظ الإعدادات بنجاح!\nسيتم تطبيق التغييرات عند إعادة تشغيل التطبيق.")
            self.dialog.destroy()
    
    def _on_cancel(self):
        """إلغاء التغييرات"""
        self.dialog.destroy()