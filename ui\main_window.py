#!/usr/bin/env python3
# -*- coding: utf-8 -*-
"""
النافذة الرئيسية - نظام نقطة البيع
Main Window for POS System
"""

import tkinter as tk
from tkinter import ttk, messagebox
from datetime import datetime
from typing import Dict, List, Optional
import threading

class MainWindow:
    """النافذة الرئيسية للنظام"""
    
    def __init__(self, parent, app_instance):
        """تهيئة النافذة الرئيسية"""
        self.parent = parent
        self.app = app_instance
        self.config = app_instance.config
        
        # إعداد النافذة الرئيسية
        self._setup_main_window()
        
        # متغيرات النظام
        self.current_order = {}
        self.order_items = []
        self.total_amount = 0.0
        
        # بيانات التطبيق
        self.categories = []
        self.menu_items = []
        self.tables = []
        
        # عناصر الواجهة
        self.category_frame = None
        self.menu_frame = None
        self.order_frame = None
        self.payment_frame = None
        self.status_bar = None
        
        # تحميل البيانات
        self._load_data()
    
    def _setup_main_window(self):
        """إعداد النافذة الرئيسية"""
        self.parent.title(f"{self.config.app_name} - {self.app.current_user['full_name']}")
        
        # تعيين الحجم والموقع
        width, height = self.config.window_size
        self.parent.geometry(f"{width}x{height}")
        
        # جعل النافذة قابلة لتغيير الحجم
        self.parent.resizable(True, True)
        
        # توسيط النافذة
        self._center_window()
        
        # تعيين الحد الأدنى للحجم
        self.parent.minsize(1000, 700)
        
        # ربط أحداث النافذة
        self.parent.protocol("WM_DELETE_WINDOW", self._on_closing)
        
        # تطبيق السمة
        self._apply_theme()
    
    def _center_window(self):
        """توسيط النافذة على الشاشة"""
        self.parent.update_idletasks()
        
        screen_width = self.parent.winfo_screenwidth()
        screen_height = self.parent.winfo_screenheight()
        
        window_width = self.parent.winfo_reqwidth()
        window_height = self.parent.winfo_reqheight()
        
        x = (screen_width - window_width) // 2
        y = (screen_height - window_height) // 2
        
        width, height = self.config.window_size
        self.parent.geometry(f"{width}x{height}+{x}+{y}")
    
    def _apply_theme(self):
        """تطبيق السمة على النافذة"""
        style = ttk.Style()
        style.theme_use('clam')
        
        # الألوان الرئيسية
        colors = {
            'primary': '#2C3E50',
            'secondary': '#3498DB',
            'success': '#27AE60',
            'warning': '#F39C12',
            'danger': '#E74C3C',
            'light': '#ECF0F1',
            'dark': '#34495E',
            'white': '#FFFFFF'
        }
        
        # تخصيص الأزرار
        style.configure(
            "Primary.TButton",
            background=colors['primary'],
            foreground=colors['white'],
            font=("Tahoma", 10, "bold"),
            padding=(10, 8)
        )
        
        style.configure(
            "Secondary.TButton",
            background=colors['secondary'],
            foreground=colors['white'],
            font=("Tahoma", 10),
            padding=(8, 6)
        )
        
        style.configure(
            "Success.TButton",
            background=colors['success'],
            foreground=colors['white'],
            font=("Tahoma", 10, "bold"),
            padding=(10, 8)
        )
        
        style.configure(
            "Warning.TButton",
            background=colors['warning'],
            foreground=colors['white'],
            font=("Tahoma", 10),
            padding=(8, 6)
        )
        
        style.configure(
            "Danger.TButton",
            background=colors['danger'],
            foreground=colors['white'],
            font=("Tahoma", 10),
            padding=(8, 6)
        )
        
        # تخصيص التسميات
        style.configure(
            "Heading.TLabel",
            font=("Tahoma", 14, "bold"),
            foreground=colors['primary']
        )
        
        style.configure(
            "Info.TLabel",
            font=("Tahoma", 10),
            foreground=colors['dark']
        )
    
    def show(self):
        """عرض النافذة الرئيسية"""
        self._create_menu_bar()
        self._create_main_interface()
        self._create_status_bar()
        self._update_status()
    
    def _create_menu_bar(self):
        """إنشاء شريط القائمة"""
        menubar = tk.Menu(self.parent)
        self.parent.config(menu=menubar)
        
        # قائمة الملف
        file_menu = tk.Menu(menubar, tearoff=0)
        menubar.add_cascade(label="ملف", menu=file_menu)
        file_menu.add_command(label="طلب جديد", command=self._new_order, accelerator="Ctrl+N")
        file_menu.add_separator()
        file_menu.add_command(label="طباعة", command=self._print_receipt, accelerator="Ctrl+P")
        file_menu.add_separator()
        file_menu.add_command(label="إعدادات", command=self._show_settings)
        file_menu.add_separator()
        file_menu.add_command(label="تسجيل خروج", command=self.app.logout)
        file_menu.add_command(label="خروج", command=self._on_closing)
        
        # قائمة الطلبات
        order_menu = tk.Menu(menubar, tearoff=0)
        menubar.add_cascade(label="الطلبات", menu=order_menu)
        order_menu.add_command(label="طلب جديد", command=self._new_order)
        order_menu.add_command(label="حفظ الطلب", command=self._save_order)
        order_menu.add_command(label="إلغاء الطلب", command=self._cancel_order)
        order_menu.add_separator()
        order_menu.add_command(label="الطلبات المحفوظة", command=self._show_saved_orders)
        order_menu.add_command(label="سجل الطلبات", command=self._show_order_history)
        
        # قائمة القائمة
        menu_menu = tk.Menu(menubar, tearoff=0)
        menubar.add_cascade(label="القائمة", menu=menu_menu)
        menu_menu.add_command(label="إدارة الأصناف", command=self._manage_menu_items)
        menu_menu.add_command(label="إدارة الفئات", command=self._manage_categories)
        menu_menu.add_separator()
        menu_menu.add_command(label="إعدادات الأسعار", command=self._price_settings)
        
        # قائمة التقارير
        reports_menu = tk.Menu(menubar, tearoff=0)
        menubar.add_cascade(label="التقارير", menu=reports_menu)
        reports_menu.add_command(label="مبيعات اليوم", command=self._daily_sales_report)
        reports_menu.add_command(label="تقرير مفصل", command=self._detailed_report)
        reports_menu.add_command(label="الأصناف الأكثر مبيعاً", command=self._top_selling_items)
        reports_menu.add_separator()
        reports_menu.add_command(label="تقرير المخزون", command=self._inventory_report)
        
        # قائمة المساعدة
        help_menu = tk.Menu(menubar, tearoff=0)
        menubar.add_cascade(label="مساعدة", menu=help_menu)
        help_menu.add_command(label="دليل المستخدم", command=self._show_user_guide)
        help_menu.add_command(label="اختصارات لوحة المفاتيح", command=self._show_shortcuts)
        help_menu.add_separator()
        help_menu.add_command(label="حول البرنامج", command=self._show_about)
        
        # ربط اختصارات لوحة المفاتيح
        self.parent.bind('<Control-n>', lambda e: self._new_order())
        self.parent.bind('<Control-p>', lambda e: self._print_receipt())
        self.parent.bind('<Control-s>', lambda e: self._save_order())
        self.parent.bind('<Escape>', lambda e: self._cancel_order())
    
    def _create_main_interface(self):
        """إنشاء الواجهة الرئيسية"""
        # الإطار الرئيسي
        main_paned = ttk.PanedWindow(self.parent, orient='horizontal')
        main_paned.pack(fill='both', expand=True, padx=5, pady=5)
        
        # الجانب الأيسر - القائمة والفئات
        left_frame = ttk.Frame(main_paned)
        main_paned.add(left_frame, weight=2)
        
        # الجانب الأيمن - الطلب والدفع
        right_frame = ttk.Frame(main_paned)
        main_paned.add(right_frame, weight=1)
        
        # إنشاء أقسام الواجهة
        self._create_category_section(left_frame)
        self._create_menu_section(left_frame)
        self._create_order_section(right_frame)
        self._create_payment_section(right_frame)
    
    def _create_category_section(self, parent):
        """إنشاء قسم الفئات"""
        # إطار الفئات
        category_label_frame = ttk.LabelFrame(parent, text="الفئات", padding="10")
        category_label_frame.pack(fill='x', pady=(0, 10))
        
        # إطار أزرار الفئات
        self.category_frame = ttk.Frame(category_label_frame)
        self.category_frame.pack(fill='x')
        
        # زر كل الأصناف
        all_btn = ttk.Button(
            self.category_frame,
            text="كل الأصناف",
            style="Primary.TButton",
            command=lambda: self._load_menu_items()
        )
        all_btn.pack(side='right', padx=2)
        
        # تحميل أزرار الفئات
        self._load_category_buttons()
    
    def _create_menu_section(self, parent):
        """إنشاء قسم القائمة"""
        # إطار القائمة
        menu_label_frame = ttk.LabelFrame(parent, text="قائمة الأصناف", padding="10")
        menu_label_frame.pack(fill='both', expand=True)
        
        # إطار التمرير
        menu_canvas = tk.Canvas(menu_label_frame, highlightthickness=0)
        menu_scrollbar = ttk.Scrollbar(menu_label_frame, orient="vertical", command=menu_canvas.yview)
        
        self.menu_frame = ttk.Frame(menu_canvas)
        
        # ربط التمرير
        self.menu_frame.bind(
            "<Configure>",
            lambda e: menu_canvas.configure(scrollregion=menu_canvas.bbox("all"))
        )
        
        menu_canvas.create_window((0, 0), window=self.menu_frame, anchor="nw")
        menu_canvas.configure(yscrollcommand=menu_scrollbar.set)
        
        # تخطيط التمرير
        menu_canvas.pack(side="left", fill="both", expand=True)
        menu_scrollbar.pack(side="right", fill="y")
        
        # ربط عجلة الفأرة
        def _on_mousewheel(event):
            menu_canvas.yview_scroll(int(-1*(event.delta/120)), "units")
        
        menu_canvas.bind("<MouseWheel>", _on_mousewheel)
        
        # تحميل عناصر القائمة
        self._load_menu_items()
    
    def _create_order_section(self, parent):
        """إنشاء قسم الطلب"""
        # إطار الطلب
        order_label_frame = ttk.LabelFrame(parent, text="الطلب الحالي", padding="10")
        order_label_frame.pack(fill='both', expand=True, pady=(0, 10))
        
        # معلومات الطلب
        order_info_frame = ttk.Frame(order_label_frame)
        order_info_frame.pack(fill='x', pady=(0, 10))
        
        # رقم الطلب
        order_number_frame = ttk.Frame(order_info_frame)
        order_number_frame.pack(fill='x', pady=2)
        
        ttk.Label(order_number_frame, text="رقم الطلب:", font=("Tahoma", 10, "bold")).pack(side='right')
        self.order_number_label = ttk.Label(
            order_number_frame,
            text="جديد",
            font=("Tahoma", 10),
            foreground="#3498DB"
        )
        self.order_number_label.pack(side='left')
        
        # نوع الطلب
        order_type_frame = ttk.Frame(order_info_frame)
        order_type_frame.pack(fill='x', pady=2)
        
        ttk.Label(order_type_frame, text="نوع الطلب:", font=("Tahoma", 10, "bold")).pack(side='right')
        
        self.order_type_var = tk.StringVar(value="dine_in")
        order_type_combo = ttk.Combobox(
            order_type_frame,
            textvariable=self.order_type_var,
            values=[("dine_in", "في المطعم"), ("takeaway", "طلب خارجي"), ("delivery", "توصيل")],
            state="readonly",
            width=15
        )
        order_type_combo.pack(side='left', padx=(5, 0))
        
        # رقم الطاولة
        table_frame = ttk.Frame(order_info_frame)
        table_frame.pack(fill='x', pady=2)
        
        ttk.Label(table_frame, text="رقم الطاولة:", font=("Tahoma", 10, "bold")).pack(side='right')
        
        self.table_var = tk.StringVar()
        table_combo = ttk.Combobox(
            table_frame,
            textvariable=self.table_var,
            values=[],  # سيتم تحديثها لاحقاً
            state="readonly",
            width=15
        )
        table_combo.pack(side='left', padx=(5, 0))
        
        # قائمة عناصر الطلب
        items_frame = ttk.Frame(order_label_frame)
        items_frame.pack(fill='both', expand=True)
        
        # عناوين الأعمدة
        headers_frame = ttk.Frame(items_frame)
        headers_frame.pack(fill='x', pady=(0, 5))
        
        ttk.Label(headers_frame, text="الصنف", font=("Tahoma", 10, "bold")).pack(side='right', padx=5)
        ttk.Label(headers_frame, text="الكمية", font=("Tahoma", 10, "bold")).pack(side='right', padx=20)
        ttk.Label(headers_frame, text="السعر", font=("Tahoma", 10, "bold")).pack(side='right', padx=5)
        ttk.Label(headers_frame, text="المجموع", font=("Tahoma", 10, "bold")).pack(side='left', padx=5)
        
        # قائمة العناصر مع التمرير
        items_canvas = tk.Canvas(items_frame, highlightthickness=0, height=300)
        items_scrollbar = ttk.Scrollbar(items_frame, orient="vertical", command=items_canvas.yview)
        
        self.order_items_frame = ttk.Frame(items_canvas)
        
        self.order_items_frame.bind(
            "<Configure>",
            lambda e: items_canvas.configure(scrollregion=items_canvas.bbox("all"))
        )
        
        items_canvas.create_window((0, 0), window=self.order_items_frame, anchor="nw")
        items_canvas.configure(yscrollcommand=items_scrollbar.set)
        
        items_canvas.pack(side="left", fill="both", expand=True)
        items_scrollbar.pack(side="right", fill="y")
        
        # أزرار إدارة الطلب
        order_buttons_frame = ttk.Frame(order_label_frame)
        order_buttons_frame.pack(fill='x', pady=(10, 0))
        
        ttk.Button(
            order_buttons_frame,
            text="طلب جديد",
            style="Secondary.TButton",
            command=self._new_order
        ).pack(side='right', padx=2)
        
        ttk.Button(
            order_buttons_frame,
            text="حفظ الطلب",
            style="Secondary.TButton",
            command=self._save_order
        ).pack(side='right', padx=2)
        
        ttk.Button(
            order_buttons_frame,
            text="طباعة للمطبخ",
            style="Warning.TButton",
            command=self._print_kitchen_order
        ).pack(side='left', padx=2)
    
    def _create_payment_section(self, parent):
        """إنشاء قسم الدفع"""
        # إطار الدفع
        payment_label_frame = ttk.LabelFrame(parent, text="الدفع والإجماليات", padding="10")
        payment_label_frame.pack(fill='x')
        
        # إجماليات الطلب
        totals_frame = ttk.Frame(payment_label_frame)
        totals_frame.pack(fill='x', pady=(0, 15))
        
        # المجموع الفرعي
        subtotal_frame = ttk.Frame(totals_frame)
        subtotal_frame.pack(fill='x', pady=2)
        
        ttk.Label(subtotal_frame, text="المجموع الفرعي:", font=("Tahoma", 10)).pack(side='right')
        self.subtotal_label = ttk.Label(
            subtotal_frame,
            text="0.00 ر.س",
            font=("Tahoma", 10, "bold")
        )
        self.subtotal_label.pack(side='left')
        
        # الخصم
        discount_frame = ttk.Frame(totals_frame)
        discount_frame.pack(fill='x', pady=2)
        
        ttk.Label(discount_frame, text="الخصم:", font=("Tahoma", 10)).pack(side='right')
        
        self.discount_var = tk.DoubleVar()
        discount_entry = ttk.Entry(
            discount_frame,
            textvariable=self.discount_var,
            width=10,
            justify='center'
        )
        discount_entry.pack(side='left', padx=(5, 0))
        discount_entry.bind('<KeyRelease>', self._calculate_totals)
        
        ttk.Label(discount_frame, text="ر.س").pack(side='left', padx=(2, 0))
        
        # الضريبة
        tax_frame = ttk.Frame(totals_frame)
        tax_frame.pack(fill='x', pady=2)
        
        ttk.Label(tax_frame, text="ضريبة القيمة المضافة:", font=("Tahoma", 10)).pack(side='right')
        self.tax_label = ttk.Label(
            tax_frame,
            text="0.00 ر.س",
            font=("Tahoma", 10, "bold")
        )
        self.tax_label.pack(side='left')
        
        # المجموع النهائي
        total_frame = ttk.Frame(totals_frame)
        total_frame.pack(fill='x', pady=(10, 0))
        
        ttk.Label(
            total_frame,
            text="المجموع النهائي:",
            font=("Tahoma", 12, "bold")
        ).pack(side='right')
        
        self.total_label = ttk.Label(
            total_frame,
            text="0.00 ر.س",
            font=("Tahoma", 14, "bold"),
            foreground="#27AE60"
        )
        self.total_label.pack(side='left')
        
        # طرق الدفع
        payment_methods_frame = ttk.Frame(payment_label_frame)
        payment_methods_frame.pack(fill='x', pady=(15, 0))
        
        ttk.Label(
            payment_methods_frame,
            text="طريقة الدفع:",
            font=("Tahoma", 11, "bold")
        ).pack(anchor='e', pady=(0, 10))
        
        # أزرار طرق الدفع
        payment_buttons_frame = ttk.Frame(payment_methods_frame)
        payment_buttons_frame.pack(fill='x')
        
        ttk.Button(
            payment_buttons_frame,
            text="💵 نقدي",
            style="Success.TButton",
            command=lambda: self._process_payment('cash')
        ).pack(fill='x', pady=2)
        
        ttk.Button(
            payment_buttons_frame,
            text="💳 بطاقة",
            style="Success.TButton",
            command=lambda: self._process_payment('card')
        ).pack(fill='x', pady=2)
        
        ttk.Button(
            payment_buttons_frame,
            text="📱 محفظة رقمية",
            style="Success.TButton",
            command=lambda: self._process_payment('digital')
        ).pack(fill='x', pady=2)
    
    def _create_status_bar(self):
        """إنشاء شريط الحالة"""
        self.status_bar = ttk.Frame(self.parent)
        self.status_bar.pack(side='bottom', fill='x', padx=5, pady=2)
        
        # معلومات المستخدم
        user_info = f"المستخدم: {self.app.current_user['full_name']} ({self.app.current_user['role']})"
        ttk.Label(self.status_bar, text=user_info, font=("Tahoma", 9)).pack(side='left')
        
        # الوقت والتاريخ
        self.time_label = ttk.Label(self.status_bar, font=("Tahoma", 9))
        self.time_label.pack(side='right')
        
        # حالة الاتصال
        self.connection_label = ttk.Label(
            self.status_bar,
            text="🟢 متصل",
            font=("Tahoma", 9)
        )
        self.connection_label.pack(side='right', padx=(0, 20))
    
    def _load_data(self):
        """تحميل البيانات من قاعدة البيانات"""
        try:
            # تحميل الفئات
            self.categories = self.app.db_manager.get_categories()
            
            # تحميل عناصر القائمة
            self.menu_items = self.app.db_manager.get_menu_items()
            
            # تحميل الطاولات
            self.tables = self.app.db_manager.get_available_tables()
            
        except Exception as e:
            messagebox.showerror("خطأ", f"خطأ في تحميل البيانات: {str(e)}")
    
    def _load_category_buttons(self):
        """تحميل أزرار الفئات"""
        for category in self.categories:
            btn = ttk.Button(
                self.category_frame,
                text=category['name_ar'],
                style="Secondary.TButton",
                command=lambda cat_id=category['id']: self._load_menu_items(cat_id)
            )
            btn.pack(side='right', padx=2)
    
    def _load_menu_items(self, category_id: Optional[int] = None):
        """تحميل عناصر القائمة"""
        # مسح العناصر الحالية
        for widget in self.menu_frame.winfo_children():
            widget.destroy()
        
        # تصفية العناصر حسب الفئة
        if category_id:
            items = [item for item in self.menu_items if item['category_id'] == category_id]
        else:
            items = self.menu_items
        
        # إنشاء أزرار العناصر
        columns = 3
        for i, item in enumerate(items):
            row = i // columns
            col = i % columns
            
            item_frame = ttk.Frame(self.menu_frame, relief='raised', borderwidth=1)
            item_frame.grid(row=row, column=col, padx=5, pady=5, sticky='nsew')
            
            # تكوين الشبكة
            self.menu_frame.grid_columnconfigure(col, weight=1)
            
            # صورة الصنف (يمكن إضافتها لاحقاً)
            image_label = ttk.Label(item_frame, text="🍖", font=("Arial", 24))
            image_label.pack(pady=(10, 5))
            
            # اسم الصنف
            name_label = ttk.Label(
                item_frame,
                text=item['name_ar'],
                font=("Tahoma", 11, "bold"),
                wraplength=150
            )
            name_label.pack(pady=2)
            
            # السعر
            price_label = ttk.Label(
                item_frame,
                text=f"{item['price']:.2f} {self.config.currency}",
                font=("Tahoma", 10),
                foreground="#27AE60"
            )
            price_label.pack(pady=2)
            
            # زر الإضافة
            add_button = ttk.Button(
                item_frame,
                text="إضافة",
                style="Primary.TButton",
                command=lambda item=item: self._add_to_order(item)
            )
            add_button.pack(pady=(5, 10), padx=10, fill='x')
    
    def _add_to_order(self, menu_item: Dict):
        """إضافة صنف إلى الطلب"""
        # البحث عن الصنف في الطلب الحالي
        existing_item = None
        for item in self.order_items:
            if item['menu_item_id'] == menu_item['id']:
                existing_item = item
                break
        
        if existing_item:
            # زيادة الكمية
            existing_item['quantity'] += 1
            existing_item['total_price'] = existing_item['quantity'] * existing_item['unit_price']
        else:
            # إضافة صنف جديد
            order_item = {
                'menu_item_id': menu_item['id'],
                'name_ar': menu_item['name_ar'],
                'unit_price': menu_item['price'],
                'quantity': 1,
                'total_price': menu_item['price'],
                'special_notes': ''
            }
            self.order_items.append(order_item)
        
        # تحديث عرض الطلب
        self._refresh_order_display()
        self._calculate_totals()
    
    def _refresh_order_display(self):
        """تحديث عرض الطلب"""
        # مسح العناصر الحالية
        for widget in self.order_items_frame.winfo_children():
            widget.destroy()
        
        # عرض عناصر الطلب
        for i, item in enumerate(self.order_items):
            item_frame = ttk.Frame(self.order_items_frame)
            item_frame.pack(fill='x', pady=2)
            
            # اسم الصنف
            name_label = ttk.Label(
                item_frame,
                text=item['name_ar'],
                font=("Tahoma", 10),
                width=20,
                anchor='e'
            )
            name_label.pack(side='right', padx=5)
            
            # أزرار الكمية
            quantity_frame = ttk.Frame(item_frame)
            quantity_frame.pack(side='right', padx=10)
            
            ttk.Button(
                quantity_frame,
                text="-",
                width=3,
                command=lambda idx=i: self._decrease_quantity(idx)
            ).pack(side='left')
            
            quantity_label = ttk.Label(
                quantity_frame,
                text=str(item['quantity']),
                font=("Tahoma", 10, "bold"),
                width=3,
                anchor='center'
            )
            quantity_label.pack(side='left', padx=5)
            
            ttk.Button(
                quantity_frame,
                text="+",
                width=3,
                command=lambda idx=i: self._increase_quantity(idx)
            ).pack(side='left')
            
            # السعر الإجمالي
            total_label = ttk.Label(
                item_frame,
                text=f"{item['total_price']:.2f}",
                font=("Tahoma", 10, "bold"),
                width=10,
                anchor='w'
            )
            total_label.pack(side='left', padx=5)
            
            # زر الحذف
            ttk.Button(
                item_frame,
                text="🗑",
                width=3,
                command=lambda idx=i: self._remove_item(idx)
            ).pack(side='left', padx=2)
    
    def _increase_quantity(self, item_index: int):
        """زيادة كمية الصنف"""
        if item_index < len(self.order_items):
            item = self.order_items[item_index]
            item['quantity'] += 1
            item['total_price'] = item['quantity'] * item['unit_price']
            
            self._refresh_order_display()
            self._calculate_totals()
    
    def _decrease_quantity(self, item_index: int):
        """تقليل كمية الصنف"""
        if item_index < len(self.order_items):
            item = self.order_items[item_index]
            if item['quantity'] > 1:
                item['quantity'] -= 1
                item['total_price'] = item['quantity'] * item['unit_price']
                
                self._refresh_order_display()
                self._calculate_totals()
            else:
                self._remove_item(item_index)
    
    def _remove_item(self, item_index: int):
        """حذف صنف من الطلب"""
        if item_index < len(self.order_items):
            if messagebox.askyesno("تأكيد الحذف", "هل تريد حذف هذا الصنف من الطلب؟"):
                del self.order_items[item_index]
                self._refresh_order_display()
                self._calculate_totals()
    
    def _calculate_totals(self, event=None):
        """حساب الإجماليات"""
        # المجموع الفرعي
        subtotal = sum(item['total_price'] for item in self.order_items)
        
        # الخصم
        discount = self.discount_var.get() if hasattr(self, 'discount_var') else 0.0
        
        # الضريبة
        tax_rate = self.config.tax_rate
        tax_amount = (subtotal - discount) * tax_rate
        
        # المجموع النهائي
        total = subtotal - discount + tax_amount
        
        # تحديث التسميات
        if hasattr(self, 'subtotal_label'):
            self.subtotal_label.config(text=f"{subtotal:.2f} {self.config.currency}")
        
        if hasattr(self, 'tax_label'):
            self.tax_label.config(text=f"{tax_amount:.2f} {self.config.currency}")
        
        if hasattr(self, 'total_label'):
            self.total_label.config(text=f"{total:.2f} {self.config.currency}")
        
        # حفظ القيم
        self.total_amount = total
    
    def _update_status(self):
        """تحديث شريط الحالة"""
        # تحديث الوقت
        current_time = datetime.now().strftime("%Y-%m-%d %H:%M:%S")
        if hasattr(self, 'time_label'):
            self.time_label.config(text=current_time)
        
        # جدولة التحديث التالي
        self.parent.after(1000, self._update_status)
    
    # معالجات الأحداث والوظائف الأخرى
    
    def _new_order(self):
        """طلب جديد"""
        if self.order_items:
            if messagebox.askyesno("طلب جديد", "هل تريد إنهاء الطلب الحالي وبدء طلب جديد؟"):
                self.order_items.clear()
                self.discount_var.set(0.0)
                self._refresh_order_display()
                self._calculate_totals()
                self.order_number_label.config(text="جديد")
        else:
            self.order_items.clear()
            self.discount_var.set(0.0)
            self._refresh_order_display()
            self._calculate_totals()
    
    def _save_order(self):
        """حفظ الطلب"""
        if not self.order_items:
            messagebox.showwarning("تحذير", "لا توجد أصناف في الطلب")
            return
        
        try:
            # إنشاء بيانات الطلب
            order_data = {
                'user_id': self.app.current_user['id'],
                'order_type': self.order_type_var.get(),
                'table_id': None,  # يمكن إضافة اختيار الطاولة
                'subtotal': sum(item['total_price'] for item in self.order_items),
                'discount_amount': self.discount_var.get(),
                'tax_amount': (sum(item['total_price'] for item in self.order_items) - self.discount_var.get()) * self.config.tax_rate,
                'total_amount': self.total_amount,
                'notes': ''
            }
            
            # حفظ الطلب في قاعدة البيانات
            order_id = self.app.db_manager.create_order(order_data)
            
            if order_id:
                # إضافة عناصر الطلب
                success = self.app.db_manager.add_order_items(order_id, self.order_items)
                
                if success:
                    messagebox.showinfo("نجح", "تم حفظ الطلب بنجاح")
                    self.order_number_label.config(text=f"#{order_id}")
                else:
                    messagebox.showerror("خطأ", "فشل في حفظ عناصر الطلب")
            else:
                messagebox.showerror("خطأ", "فشل في حفظ الطلب")
        
        except Exception as e:
            messagebox.showerror("خطأ", f"خطأ في حفظ الطلب: {str(e)}")
    
    def _cancel_order(self):
        """إلغاء الطلب"""
        if self.order_items:
            if messagebox.askyesno("إلغاء الطلب", "هل تريد إلغاء الطلب الحالي؟"):
                self._new_order()
    
    def _process_payment(self, payment_method: str):
        """معالجة الدفع"""
        if not self.order_items:
            messagebox.showwarning("تحذير", "لا توجد أصناف في الطلب")
            return
        
        if self.total_amount <= 0:
            messagebox.showwarning("تحذير", "المبلغ الإجمالي غير صحيح")
            return
        
        try:
            from ui.payment_dialog import PaymentDialog
            
            # إعداد بيانات الطلب
            order_data = {
                'subtotal': sum(item['total_price'] for item in self.order_items),
                'discount_amount': self.discount_var.get() if hasattr(self, 'discount_var') else 0.0,
                'tax_amount': (sum(item['total_price'] for item in self.order_items) - (self.discount_var.get() if hasattr(self, 'discount_var') else 0.0)) * self.config.tax_rate,
                'total_amount': self.total_amount
            }
            
            # عرض نافذة الدفع
            payment_dialog = PaymentDialog(self.parent, self.total_amount, self.config, order_data)
            payment_result = payment_dialog.show()
            
            if payment_result:
                # معالجة الدفع وطباعة الفاتورة
                self._finalize_order(payment_result)
        
        except ImportError:
            # إذا لم تكن نافذة الدفع متوفرة، استخدم التأكيد البسيط
            result = messagebox.askyesno(
                "تأكيد الدفع",
                f"تأكيد الدفع بمبلغ {self.total_amount:.2f} {self.config.currency}؟"
            )
            
            if result:
                # إنشاء بيانات دفع بسيطة
                payment_data = {
                    'payment_method': payment_method,
                    'total_amount': self.total_amount,
                    'received_amount': self.total_amount,
                    'change_amount': 0,
                    'print_receipt': True
                }
                self._finalize_order(payment_data)
        
        except Exception as e:
            messagebox.showerror("خطأ", f"خطأ في معالجة الدفع: {str(e)}")
    
    def _finalize_order(self, payment_data: dict):
        """إنهاء الطلب"""
        try:
            # طباعة الفاتورة إذا كان مطلوباً
            if payment_data.get('print_receipt', True):
                self._print_receipt_with_payment(payment_data)
            
            # مسح الطلب الحالي
            self._new_order()
            
            messagebox.showinfo("نجح", "تم إنهاء الطلب ومعالجة الدفع بنجاح")
        
        except Exception as e:
            messagebox.showerror("خطأ", f"خطأ في إنهاء الطلب: {str(e)}")
    
    def _print_receipt_with_payment(self, payment_data: dict):
        """طباعة الفاتورة مع معلومات الدفع"""
        try:
            # إعداد بيانات الفاتورة
            receipt_data = {
                'order_number': self.order_number_label.cget("text"),
                'cashier_name': self.app.current_user['full_name'],
                'items': self.order_items,
                'subtotal': sum(item['total_price'] for item in self.order_items),
                'discount_amount': self.discount_var.get() if hasattr(self, 'discount_var') else 0.0,
                'tax_amount': (sum(item['total_price'] for item in self.order_items) - (self.discount_var.get() if hasattr(self, 'discount_var') else 0.0)) * self.config.tax_rate,
                'total_amount': self.total_amount,
                'order_type': self.order_type_var.get()
            }
            
            # طباعة الفاتورة مع معلومات الدفع
            self._print_receipt_internal(receipt_data, payment_data)
            
        except Exception as e:
            messagebox.showerror("خطأ", f"خطأ في طباعة الفاتورة: {str(e)}")
    
    def _print_receipt_internal(self, receipt_data: dict, payment_data: dict = None):
        """طباعة الفاتورة - التنفيذ الداخلي"""
        try:
            from utils.printer import ThermalPrinter
            
            # إنشاء مدير الطباعة
            printer = ThermalPrinter(self.config)
            
            # طباعة الفاتورة
            success = printer.print_receipt(receipt_data, payment_data)
            
            if success:
                print("✅ تم طباعة الفاتورة بنجاح")
            else:
                print("❌ فشل في طباعة الفاتورة")
                messagebox.showwarning("تحذير", "فشل في طباعة الفاتورة. تحقق من إعدادات الطابعة.")
            
        except ImportError:
            print("⚠️ وحدة الطباعة غير متوفرة - سيتم عرض الفاتورة على الشاشة")
            self._show_receipt_preview(receipt_data, payment_data)
        except Exception as e:
            print(f"❌ خطأ في الطباعة: {str(e)}")
            messagebox.showerror("خطأ", f"خطأ في طباعة الفاتورة: {str(e)}")
    
    def _show_receipt_preview(self, receipt_data: dict, payment_data: dict = None):
        """عرض معاينة الفاتورة"""
        preview_window = tk.Toplevel(self.parent)
        preview_window.title("معاينة الفاتورة")
        preview_window.geometry("400x600")
        
        # إنشاء محتوى الفاتورة
        content = self._generate_receipt_content(receipt_data, payment_data)
        
        # عرض المحتوى
        text_widget = tk.Text(preview_window, wrap=tk.WORD, font=("Courier New", 10))
        text_widget.pack(fill='both', expand=True, padx=10, pady=10)
        text_widget.insert('1.0', content)
        text_widget.config(state='disabled')
        
        # زر الإغلاق
        ttk.Button(preview_window, text="إغلاق", command=preview_window.destroy).pack(pady=10)
    
    def _generate_receipt_content(self, receipt_data: dict, payment_data: dict = None) -> str:
        """إنشاء محتوى الفاتورة"""
        from datetime import datetime
        
        lines = []
        
        # رأس الفاتورة
        lines.append("=" * 32)
        lines.append(f"{self.config.restaurant_name}".center(32))
        lines.append("=" * 32)
        lines.append(f"رقم الطلب: {receipt_data['order_number']}")
        lines.append(f"التاريخ: {datetime.now().strftime('%Y-%m-%d %H:%M')}")
        lines.append(f"الكاشير: {receipt_data['cashier_name']}")
        lines.append("-" * 32)
        
        # عناصر الطلب
        for item in receipt_data['items']:
            lines.append(f"{item['name_ar']}")
            lines.append(f"  {item['quantity']} x {item['unit_price']:.2f} = {item['total_price']:.2f}")
        
        lines.append("-" * 32)
        
        # الإجماليات
        lines.append(f"المجموع الفرعي: {receipt_data['subtotal']:.2f}")
        if receipt_data['discount_amount'] > 0:
            lines.append(f"الخصم: -{receipt_data['discount_amount']:.2f}")
        if receipt_data['tax_amount'] > 0:
            lines.append(f"ضريبة ق.م.: {receipt_data['tax_amount']:.2f}")
        lines.append(f"المجموع النهائي: {receipt_data['total_amount']:.2f}")
        
        # معلومات الدفع
        if payment_data:
            lines.append("-" * 32)
            method_ar = {
                'cash': 'نقدي',
                'card': 'بطاقة',
                'digital': 'محفظة رقمية'
            }.get(payment_data['payment_method'], 'نقدي')
            
            lines.append(f"طريقة الدفع: {method_ar}")
            if payment_data['payment_method'] == 'cash' and 'received_amount' in payment_data:
                lines.append(f"المبلغ المستلم: {payment_data['received_amount']:.2f}")
                if payment_data.get('change_amount', 0) > 0:
                    lines.append(f"الباقي: {payment_data['change_amount']:.2f}")
        
        lines.append("=" * 32)
        lines.append("شكراً لزيارتكم".center(32))
        lines.append("=" * 32)
        
        return '\n'.join(lines)
    
    def _print_receipt(self):
        """طباعة الفاتورة"""
        if not self.order_items:
            messagebox.showwarning("تحذير", "لا توجد أصناف للطباعة")
            return
        
        try:
            # إعداد بيانات الفاتورة
            receipt_data = {
                'order_number': self.order_number_label.cget("text"),
                'cashier_name': self.app.current_user['full_name'],
                'items': self.order_items,
                'subtotal': sum(item['total_price'] for item in self.order_items),
                'discount_amount': self.discount_var.get(),
                'tax_amount': (sum(item['total_price'] for item in self.order_items) - self.discount_var.get()) * self.config.tax_rate,
                'total_amount': self.total_amount,
                'order_type': self.order_type_var.get()
            }
            
            # طباعة الفاتورة (سيتم تنفيذها لاحقاً)
            print("طباعة الفاتورة...")  # مؤقت
            
        except Exception as e:
            messagebox.showerror("خطأ", f"خطأ في طباعة الفاتورة: {str(e)}")
    
    def _print_kitchen_order(self):
        """طباعة طلب المطبخ"""
        if not self.order_items:
            messagebox.showwarning("تحذير", "لا توجد أصناف للطباعة")
            return
        
        print("طباعة طلب المطبخ...")  # مؤقت
    
    # الوظائف الأخرى (ستكمل في الملفات التالية)
    
    def _show_settings(self):
        """عرض الإعدادات"""
        try:
            from ui.settings_dialog import SettingsDialog
            dialog = SettingsDialog(self.parent, self.config, self.app)
            dialog.show()
        except Exception as e:
            messagebox.showerror("خطأ", f"خطأ في فتح الإعدادات: {str(e)}")
    
    def _show_saved_orders(self):
        """عرض الطلبات المحفوظة"""
        messagebox.showinfo("الطلبات المحفوظة", "ستتوفر قريباً")
    
    def _show_order_history(self):
        """عرض سجل الطلبات"""
        messagebox.showinfo("سجل الطلبات", "ستتوفر قريباً")
    
    def _manage_menu_items(self):
        """إدارة عناصر القائمة"""
        messagebox.showinfo("إدارة الأصناف", "ستتوفر قريباً")
    
    def _manage_categories(self):
        """إدارة الفئات"""
        messagebox.showinfo("إدارة الفئات", "ستتوفر قريباً")
    
    def _price_settings(self):
        """إعدادات الأسعار"""
        messagebox.showinfo("إعدادات الأسعار", "ستتوفر قريباً")
    
    def _daily_sales_report(self):
        """تقرير مبيعات اليوم"""
        try:
            sales_data = self.app.db_manager.get_daily_sales()
            
            report_text = f"""
تقرير مبيعات اليوم
===============

إجمالي الطلبات: {sales_data.get('total_orders', 0)}
إجمالي المبيعات: {sales_data.get('total_sales', 0):.2f} {self.config.currency}
المبيعات النقدية: {sales_data.get('cash_sales', 0):.2f} {self.config.currency}
مبيعات البطاقات: {sales_data.get('card_sales', 0):.2f} {self.config.currency}
المبيعات الرقمية: {sales_data.get('digital_sales', 0):.2f} {self.config.currency}
إجمالي الضرائب: {sales_data.get('total_tax', 0):.2f} {self.config.currency}
إجمالي الخصومات: {sales_data.get('total_discount', 0):.2f} {self.config.currency}
            """
            
            messagebox.showinfo("تقرير المبيعات", report_text)
        
        except Exception as e:
            messagebox.showerror("خطأ", f"خطأ في إنشاء التقرير: {str(e)}")
    
    def _detailed_report(self):
        """تقرير مفصل"""
        try:
            from reports.sales_report import SalesReportWindow
            report_window = SalesReportWindow(self.parent, self.app.db_manager, self.config)
            report_window.show()
        except Exception as e:
            messagebox.showerror("خطأ", f"خطأ في فتح التقرير: {str(e)}")
    
    def _top_selling_items(self):
        """الأصناف الأكثر مبيعاً"""
        messagebox.showinfo("الأصناف الأكثر مبيعاً", "ستتوفر قريباً")
    
    def _inventory_report(self):
        """تقرير المخزون"""
        try:
            from ui.inventory_dialog import InventoryDialog
            dialog = InventoryDialog(self.parent, self.app.db_manager, self.config)
            dialog.show()
        except Exception as e:
            messagebox.showerror("خطأ", f"خطأ في فتح إدارة المخزون: {str(e)}")
    
    def _show_user_guide(self):
        """دليل المستخدم"""
        messagebox.showinfo("دليل المستخدم", "ستتوفر قريباً")
    
    def _show_shortcuts(self):
        """اختصارات لوحة المفاتيح"""
        shortcuts_text = """
اختصارات لوحة المفاتيح
==================

Ctrl + N : طلب جديد
Ctrl + S : حفظ الطلب
Ctrl + P : طباعة الفاتورة
Escape : إلغاء الطلب
F1 : المساعدة
        """
        messagebox.showinfo("اختصارات لوحة المفاتيح", shortcuts_text)
    
    def _show_about(self):
        """حول البرنامج"""
        about_text = f"""
{self.config.app_name}
الإصدار 1.0.0

نظام نقطة بيع متكامل لمطعم شاورما الدجاج

تم التطوير بواسطة فريق التطوير
البريد الإلكتروني: <EMAIL>

© 2024 جميع الحقوق محفوظة
        """
        messagebox.showinfo("حول البرنامج", about_text)
    
    def _on_closing(self):
        """معالج إغلاق النافذة"""
        if self.order_items:
            result = messagebox.askyesnocancel(
                "إغلاق التطبيق",
                "يوجد طلب غير مكتمل. هل تريد حفظه قبل الخروج؟"
            )
            
            if result is True:  # نعم، احفظ
                self._save_order()
                self.parent.quit()
            elif result is False:  # لا، لا تحفظ
                self.parent.quit()
            # إذا كان None (إلغاء)، لا تفعل شيئاً
        else:
            if messagebox.askyesno("إغلاق التطبيق", "هل تريد إغلاق التطبيق؟"):
                self.parent.quit()
    
    def destroy(self):
        """تدمير النافذة"""
        # يمكن إضافة تنظيف الموارد هنا
        pass