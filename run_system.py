#!/usr/bin/env python3
# -*- coding: utf-8 -*-
"""
تشغيل نظام نقطة البيع - شاورما الدجاج
Run Shawarma Chicken POS System
"""

import sys
import os
import traceback

def check_python_version():
    """فحص إصدار Python"""
    if sys.version_info < (3, 7):
        print("❌ يتطلب Python 3.7 أو أحدث")
        print(f"الإصدار الحالي: {sys.version}")
        return False
    
    print(f"✅ Python {sys.version_info.major}.{sys.version_info.minor}.{sys.version_info.micro}")
    return True

def check_imports():
    """فحص المكتبات المطلوبة"""
    required_modules = {
        'tkinter': 'واجهة المستخدم الرسومية',
        'sqlite3': 'قاعدة البيانات',
        'json': 'معالجة JSON',
        'datetime': 'التاريخ والوقت',
        'hashlib': 'تشفير كلمات المرور',
        'threading': 'المعالجة المتوازية'
    }
    
    missing = []
    
    for module, description in required_modules.items():
        try:
            __import__(module)
            print(f"✅ {module} - {description}")
        except ImportError:
            print(f"❌ {module} - {description}")
            missing.append(module)
    
    if missing:
        print(f"\nالمكتبات المفقودة: {', '.join(missing)}")
        return False
    
    return True

def check_files():
    """فحص الملفات الأساسية"""
    required_files = [
        'main.py',
        'config.json',
        'database/__init__.py',
        'database/db_manager.py',
        'ui/__init__.py',
        'ui/login_window.py',
        'ui/main_window.py',
        'ui/payment_dialog.py',
        'ui/settings_dialog.py',
        'ui/inventory_dialog.py',
        'utils/__init__.py',
        'utils/config.py',
        'utils/printer.py',
        'reports/__init__.py',
        'reports/sales_report.py'
    ]
    
    missing = []
    
    for file_path in required_files:
        if os.path.exists(file_path):
            print(f"✅ {file_path}")
        else:
            print(f"❌ {file_path}")
            missing.append(file_path)
    
    if missing:
        print(f"\nالملفات المفقودة: {', '.join(missing)}")
        return False
    
    return True

def create_logs_directory():
    """إنشاء مجلد السجلات"""
    if not os.path.exists('logs'):
        os.makedirs('logs')
        print("✅ تم إنشاء مجلد السجلات")
    else:
        print("✅ مجلد السجلات موجود")

def create_backups_directory():
    """إنشاء مجلد النسخ الاحتياطية"""
    if not os.path.exists('backups'):
        os.makedirs('backups')
        print("✅ تم إنشاء مجلد النسخ الاحتياطية")
    else:
        print("✅ مجلد النسخ الاحتياطية موجود")

def run_system():
    """تشغيل النظام"""
    print("\n" + "="*60)
    print("🚀 بدء تشغيل نظام نقطة البيع - شاورما الدجاج")
    print("="*60)
    
    try:
        # استيراد وتشغيل التطبيق الرئيسي
        from main import POSApp
        
        print("✅ تم تحميل التطبيق بنجاح")
        print("🔄 بدء تشغيل الواجهة الرسومية...")
        
        app = POSApp()
        app.run()
        
        print("✅ تم إغلاق النظام بنجاح")
        
    except ImportError as e:
        print(f"❌ خطأ في استيراد الوحدات: {str(e)}")
        traceback.print_exc()
        return False
    
    except Exception as e:
        print(f"❌ خطأ في تشغيل النظام: {str(e)}")
        traceback.print_exc()
        return False
    
    return True

def main():
    """الدالة الرئيسية"""
    print("🍖 نظام نقطة البيع - مطعم شاورما الدجاج")
    print("=" * 50)
    
    # فحص المتطلبات
    print("\n📋 فحص المتطلبات:")
    print("-" * 20)
    
    if not check_python_version():
        input("اضغط Enter للخروج...")
        return
    
    print("\n📦 فحص المكتبات:")
    print("-" * 20)
    
    if not check_imports():
        input("اضغط Enter للخروج...")
        return
    
    print("\n📁 فحص الملفات:")
    print("-" * 20)
    
    if not check_files():
        input("اضغط Enter للخروج...")
        return
    
    print("\n🔧 إعداد المجلدات:")
    print("-" * 20)
    
    create_logs_directory()
    create_backups_directory()
    
    print("\n✅ جميع المتطلبات متوفرة!")
    
    # تشغيل النظام
    if run_system():
        print("\n👋 شكراً لاستخدام نظام نقطة البيع")
    else:
        print("\n⚠️ حدث خطأ أثناء التشغيل")
        input("اضغط Enter للخروج...")

if __name__ == "__main__":
    main()