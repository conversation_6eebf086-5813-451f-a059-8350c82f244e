#!/usr/bin/env python3
# -*- coding: utf-8 -*-
"""
فحص سريع للنظام
Quick System Check
"""

import sys
import os

print("فحص سريع لنظام نقطة البيع")
print("=" * 40)

# فحص Python
print(f"إصدار Python: {sys.version}")

# فحص المجلد الحالي
print(f"المجلد الحالي: {os.getcwd()}")

# فحص الملفات الأساسية
files_to_check = ['main.py', 'config.json']
for file in files_to_check:
    if os.path.exists(file):
        print(f"✅ {file} موجود")
    else:
        print(f"❌ {file} غير موجود")

# فحص المجلدات
dirs_to_check = ['database', 'ui', 'utils', 'reports']
for dir in dirs_to_check:
    if os.path.exists(dir):
        print(f"✅ مجلد {dir} موجود")
    else:
        print(f"❌ مجلد {dir} غير موجود")

# فحص استيراد المكتبات الأساسية
try:
    import tkinter
    print("✅ tkinter متوفر")
except ImportError:
    print("❌ tkinter غير متوفر")

try:
    import sqlite3
    print("✅ sqlite3 متوفر")
except ImportError:
    print("❌ sqlite3 غير متوفر")

print("=" * 40)
print("انتهى الفحص")