#!/usr/bin/env python3
# -*- coding: utf-8 -*-
"""
إصلاح قاعدة البيانات وكلمات المرور
Fix database and passwords
"""

import sqlite3
import hashlib
import os

def hash_password(password: str) -> str:
    """تشفير كلمة المرور"""
    return hashlib.sha256(password.encode()).hexdigest()

def fix_database():
    """إصلاح قاعدة البيانات"""
    db_path = 'pos_database.db'
    
    if not os.path.exists(db_path):
        print("❌ ملف قاعدة البيانات غير موجود")
        return False
    
    try:
        # الاتصال بقاعدة البيانات
        conn = sqlite3.connect(db_path)
        conn.row_factory = sqlite3.Row
        cursor = conn.cursor()
        
        print("🔍 فحص المستخدمين الموجودين...")
        
        # فحص المستخدمين
        cursor.execute('SELECT username, full_name, role FROM users')
        users = cursor.fetchall()
        
        if not users:
            print("❌ لا يوجد مستخدمون في قاعدة البيانات")
            
            # إنشاء المستخدمين الافتراضيين
            print("📝 إنشاء المستخدمين الافتراضيين...")
            
            # المدير
            admin_hash = hash_password('admin123')
            cursor.execute('''
                INSERT INTO users (username, password_hash, full_name, role, phone, email, is_active)
                VALUES (?, ?, ?, ?, ?, ?, ?)
            ''', ('admin', admin_hash, 'مدير النظام', 'admin', '01234567890', '<EMAIL>', 1))
            
            # الكاشير
            cashier_hash = hash_password('cashier123')
            cursor.execute('''
                INSERT INTO users (username, password_hash, full_name, role, phone, email, is_active)
                VALUES (?, ?, ?, ?, ?, ?, ?)
            ''', ('cashier', cashier_hash, 'محمد أحمد', 'cashier', '01234567891', '<EMAIL>', 1))
            
            conn.commit()
            print("✅ تم إنشاء المستخدمين الافتراضيين")
        
        else:
            print(f"📋 تم العثور على {len(users)} مستخدم:")
            for user in users:
                print(f"   - {user['username']} ({user['full_name']}) - {user['role']}")
            
            # تحديث كلمات المرور
            print("🔐 تحديث كلمات المرور...")
            
            admin_hash = hash_password('admin123')
            cashier_hash = hash_password('cashier123')
            
            cursor.execute('UPDATE users SET password_hash = ? WHERE username = ?', (admin_hash, 'admin'))
            cursor.execute('UPDATE users SET password_hash = ? WHERE username = ?', (cashier_hash, 'cashier'))
            
            conn.commit()
            print("✅ تم تحديث كلمات المرور")
        
        # اختبار كلمات المرور
        print("🧪 اختبار كلمات المرور...")
        
        # اختبار المدير
        admin_test_hash = hash_password('admin123')
        cursor.execute('SELECT * FROM users WHERE username = ? AND password_hash = ? AND is_active = 1', 
                      ('admin', admin_test_hash))
        admin_result = cursor.fetchone()
        
        if admin_result:
            print("✅ كلمة مرور المدير تعمل بشكل صحيح")
        else:
            print("❌ مشكلة في كلمة مرور المدير")
        
        # اختبار الكاشير
        cashier_test_hash = hash_password('cashier123')
        cursor.execute('SELECT * FROM users WHERE username = ? AND password_hash = ? AND is_active = 1', 
                      ('cashier', cashier_test_hash))
        cashier_result = cursor.fetchone()
        
        if cashier_result:
            print("✅ كلمة مرور الكاشير تعمل بشكل صحيح")
        else:
            print("❌ مشكلة في كلمة مرور الكاشير")
        
        conn.close()
        
        print("\n🎯 معلومات تسجيل الدخول:")
        print("=" * 40)
        print("👨‍💼 المدير:")
        print("   اسم المستخدم: admin")
        print("   كلمة المرور: admin123")
        print()
        print("💰 الكاشير:")
        print("   اسم المستخدم: cashier")
        print("   كلمة المرور: cashier123")
        print("=" * 40)
        
        return True
        
    except Exception as e:
        print(f"❌ خطأ في إصلاح قاعدة البيانات: {str(e)}")
        return False

def main():
    """الدالة الرئيسية"""
    print("🔧 إصلاح قاعدة البيانات وكلمات المرور")
    print("=" * 50)
    
    if fix_database():
        print("\n✅ تم إصلاح قاعدة البيانات بنجاح")
        print("💡 يمكنك الآن تشغيل النظام باستخدام: python startup.py")
    else:
        print("\n❌ فشل في إصلاح قاعدة البيانات")

if __name__ == "__main__":
    main()
