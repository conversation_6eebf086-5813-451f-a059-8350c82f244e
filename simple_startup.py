#!/usr/bin/env python3
# -*- coding: utf-8 -*-
"""
تشغيل مبسط لنظام نقطة البيع
Simple startup for POS System
"""

import sys
import os
import tkinter as tk
from tkinter import messagebox

def main():
    """الدالة الرئيسية"""
    print("🍖 مرحباً بك في نظام نقطة البيع - مطعم شاورما الدجاج")
    print("=" * 60)
    
    try:
        # إضافة مسار المشروع
        project_path = os.path.dirname(os.path.abspath(__file__))
        if project_path not in sys.path:
            sys.path.insert(0, project_path)
        
        print(f"مسار المشروع: {project_path}")
        
        # اختبار tkinter
        print("🧪 اختبار واجهة المستخدم الرسومية...")
        root = tk.Tk()
        root.withdraw()
        
        # اختبار إنشاء نافذة
        test_window = tk.Toplevel(root)
        test_window.title("اختبار")
        test_window.geometry("300x200")
        test_window.withdraw()
        test_window.destroy()
        
        print("✅ واجهة المستخدم الرسومية تعمل")
        
        # استيراد وتشغيل التطبيق
        print("📱 تحميل التطبيق...")
        from main import ShawarmaChickenPOS
        
        # إنشاء التطبيق
        print("🚀 إنشاء التطبيق...")
        app = ShawarmaChickenPOS()
        
        print("✅ تم تحميل التطبيق بنجاح")
        print("📱 بدء التشغيل...")
        
        # تشغيل التطبيق
        app.run()
        
        print("👋 تم إغلاق النظام")
        
    except ImportError as e:
        print(f"❌ خطأ في الاستيراد: {str(e)}")
        print("💡 تأكد من وجود جميع الملفات المطلوبة")
        input("اضغط Enter للخروج...")
        
    except Exception as e:
        print(f"❌ خطأ في تشغيل النظام: {str(e)}")
        print("\n📋 تفاصيل الخطأ:")
        import traceback
        traceback.print_exc()
        input("اضغط Enter للخروج...")

if __name__ == "__main__":
    main()
