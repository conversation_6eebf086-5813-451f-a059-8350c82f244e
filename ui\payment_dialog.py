#!/usr/bin/env python3
# -*- coding: utf-8 -*-
"""
نافذة الدفع - نظام نقطة البيع
Payment Dialog for POS System
"""

import tkinter as tk
from tkinter import ttk, messagebox
from typing import Dict, Optional
from utils.printer import ThermalPrinter

class PaymentDialog:
    """نافذة الدفع"""
    
    def __init__(self, parent, total_amount: float, config, order_data: Dict):
        """تهيئة نافذة الدفع"""
        self.parent = parent
        self.total_amount = total_amount
        self.config = config
        self.order_data = order_data
        
        self.dialog = None
        self.result = None
        
        # متغيرات النموذج
        self.payment_method_var = tk.StringVar(value="cash")
        self.received_amount_var = tk.DoubleVar(value=total_amount)
        self.change_amount_var = tk.DoubleVar(value=0.0)
        self.customer_name_var = tk.StringVar()
        self.customer_phone_var = tk.StringVar()
    
    def show(self) -> Optional[Dict]:
        """عرض نافذة الدفع"""
        self.dialog = tk.Toplevel(self.parent)
        self.dialog.title("الدفع")
        self.dialog.geometry("500x600")
        self.dialog.resizable(False, False)
        
        # توسيط النافذة
        self._center_dialog()
        
        # جعل النافذة مودالية
        self.dialog.transient(self.parent)
        self.dialog.grab_set()
        
        # منع إغلاق النافذة بالـ X
        self.dialog.protocol("WM_DELETE_WINDOW", self._on_cancel)
        
        # إنشاء الواجهة
        self._create_widgets()
        
        # انتظار النتيجة
        self.dialog.wait_window()
        
        return self.result
    
    def _center_dialog(self):
        """توسيط النافذة"""
        self.dialog.update_idletasks()
        
        parent_x = self.parent.winfo_rootx()
        parent_y = self.parent.winfo_rooty()
        parent_width = self.parent.winfo_width()
        parent_height = self.parent.winfo_height()
        
        dialog_width = 500
        dialog_height = 600
        
        x = parent_x + (parent_width - dialog_width) // 2
        y = parent_y + (parent_height - dialog_height) // 2
        
        self.dialog.geometry(f"{dialog_width}x{dialog_height}+{x}+{y}")
    
    def _create_widgets(self):
        """إنشاء عناصر الواجهة"""
        # الإطار الرئيسي
        main_frame = ttk.Frame(self.dialog, padding="20")
        main_frame.pack(fill='both', expand=True)
        
        # العنوان
        title_label = ttk.Label(
            main_frame,
            text="💳 معالجة الدفع",
            font=("Tahoma", 18, "bold"),
            foreground="#2C3E50"
        )
        title_label.pack(pady=(0, 20))
        
        # معلومات الطلب
        self._create_order_summary(main_frame)
        
        # معلومات العميل
        self._create_customer_info(main_frame)
        
        # طريقة الدفع
        self._create_payment_method(main_frame)
        
        # تفاصيل الدفع
        self._create_payment_details(main_frame)
        
        # أزرار التحكم
        self._create_control_buttons(main_frame)
    
    def _create_order_summary(self, parent):
        """إنشاء ملخص الطلب"""
        summary_frame = ttk.LabelFrame(parent, text="ملخص الطلب", padding="15")
        summary_frame.pack(fill='x', pady=(0, 15))
        
        # المجموع الفرعي
        subtotal_frame = ttk.Frame(summary_frame)
        subtotal_frame.pack(fill='x', pady=2)
        
        ttk.Label(subtotal_frame, text="المجموع الفرعي:", font=("Tahoma", 11)).pack(side='right')
        subtotal_label = ttk.Label(
            subtotal_frame,
            text=f"{self.order_data.get('subtotal', 0):.2f} {self.config.currency}",
            font=("Tahoma", 11, "bold")
        )
        subtotal_label.pack(side='left')
        
        # الخصم
        discount = self.order_data.get('discount_amount', 0)
        if discount > 0:
            discount_frame = ttk.Frame(summary_frame)
            discount_frame.pack(fill='x', pady=2)
            
            ttk.Label(discount_frame, text="الخصم:", font=("Tahoma", 11)).pack(side='right')
            discount_label = ttk.Label(
                discount_frame,
                text=f"-{discount:.2f} {self.config.currency}",
                font=("Tahoma", 11, "bold"),
                foreground="#E74C3C"
            )
            discount_label.pack(side='left')
        
        # الضريبة
        tax = self.order_data.get('tax_amount', 0)
        if tax > 0:
            tax_frame = ttk.Frame(summary_frame)
            tax_frame.pack(fill='x', pady=2)
            
            ttk.Label(tax_frame, text="ضريبة القيمة المضافة:", font=("Tahoma", 11)).pack(side='right')
            tax_label = ttk.Label(
                tax_frame,
                text=f"{tax:.2f} {self.config.currency}",
                font=("Tahoma", 11, "bold")
            )
            tax_label.pack(side='left')
        
        # خط فاصل
        ttk.Separator(summary_frame).pack(fill='x', pady=10)
        
        # المجموع النهائي
        total_frame = ttk.Frame(summary_frame)
        total_frame.pack(fill='x')
        
        ttk.Label(
            total_frame,
            text="المجموع النهائي:",
            font=("Tahoma", 14, "bold")
        ).pack(side='right')
        
        total_label = ttk.Label(
            total_frame,
            text=f"{self.total_amount:.2f} {self.config.currency}",
            font=("Tahoma", 16, "bold"),
            foreground="#27AE60"
        )
        total_label.pack(side='left')
    
    def _create_customer_info(self, parent):
        """إنشاء معلومات العميل"""
        customer_frame = ttk.LabelFrame(parent, text="معلومات العميل (اختيارية)", padding="15")
        customer_frame.pack(fill='x', pady=(0, 15))
        
        # اسم العميل
        name_frame = ttk.Frame(customer_frame)
        name_frame.pack(fill='x', pady=5)
        
        ttk.Label(name_frame, text="اسم العميل:", font=("Tahoma", 11)).pack(side='right', padx=(0, 10))
        name_entry = ttk.Entry(
            name_frame,
            textvariable=self.customer_name_var,
            font=("Tahoma", 11),
            width=25
        )
        name_entry.pack(side='left', fill='x', expand=True)
        
        # رقم الهاتف
        phone_frame = ttk.Frame(customer_frame)
        phone_frame.pack(fill='x', pady=5)
        
        ttk.Label(phone_frame, text="رقم الهاتف:", font=("Tahoma", 11)).pack(side='right', padx=(0, 10))
        phone_entry = ttk.Entry(
            phone_frame,
            textvariable=self.customer_phone_var,
            font=("Tahoma", 11),
            width=25
        )
        phone_entry.pack(side='left', fill='x', expand=True)
    
    def _create_payment_method(self, parent):
        """إنشاء طريقة الدفع"""
        method_frame = ttk.LabelFrame(parent, text="طريقة الدفع", padding="15")
        method_frame.pack(fill='x', pady=(0, 15))
        
        # أزرار الاختيار
        buttons_frame = ttk.Frame(method_frame)
        buttons_frame.pack(fill='x')
        
        # نقدي
        cash_btn = ttk.Radiobutton(
            buttons_frame,
            text="💵 نقدي",
            variable=self.payment_method_var,
            value="cash",
            command=self._on_payment_method_changed
        )
        cash_btn.pack(side='right', padx=10)
        
        # بطاقة
        card_btn = ttk.Radiobutton(
            buttons_frame,
            text="💳 بطاقة ائتمان",
            variable=self.payment_method_var,
            value="card",
            command=self._on_payment_method_changed
        )
        card_btn.pack(side='right', padx=10)
        
        # محفظة رقمية
        digital_btn = ttk.Radiobutton(
            buttons_frame,
            text="📱 محفظة رقمية",
            variable=self.payment_method_var,
            value="digital",
            command=self._on_payment_method_changed
        )
        digital_btn.pack(side='right', padx=10)
    
    def _create_payment_details(self, parent):
        """إنشاء تفاصيل الدفع"""
        self.details_frame = ttk.LabelFrame(parent, text="تفاصيل الدفع النقدي", padding="15")
        self.details_frame.pack(fill='x', pady=(0, 15))
        
        # المبلغ المستحق
        due_frame = ttk.Frame(self.details_frame)
        due_frame.pack(fill='x', pady=5)
        
        ttk.Label(due_frame, text="المبلغ المستحق:", font=("Tahoma", 11, "bold")).pack(side='right')
        due_label = ttk.Label(
            due_frame,
            text=f"{self.total_amount:.2f} {self.config.currency}",
            font=("Tahoma", 11, "bold"),
            foreground="#E74C3C"
        )
        due_label.pack(side='left')
        
        # المبلغ المستلم
        self.received_frame = ttk.Frame(self.details_frame)
        self.received_frame.pack(fill='x', pady=5)
        
        ttk.Label(self.received_frame, text="المبلغ المستلم:", font=("Tahoma", 11)).pack(side='right', padx=(0, 10))
        self.received_entry = ttk.Entry(
            self.received_frame,
            textvariable=self.received_amount_var,
            font=("Tahoma", 11),
            width=15,
            justify='center'
        )
        self.received_entry.pack(side='left')
        self.received_entry.bind('<KeyRelease>', self._calculate_change)
        
        ttk.Label(self.received_frame, text=self.config.currency).pack(side='left', padx=(5, 0))
        
        # أزرار المبالغ السريعة
        self.quick_amounts_frame = ttk.Frame(self.details_frame)
        self.quick_amounts_frame.pack(fill='x', pady=10)
        
        ttk.Label(self.quick_amounts_frame, text="مبالغ سريعة:", font=("Tahoma", 10)).pack(side='right')
        
        quick_amounts = [50, 100, 200, 500]
        for amount in quick_amounts:
            btn = ttk.Button(
                self.quick_amounts_frame,
                text=f"{amount}",
                width=8,
                command=lambda amt=amount: self._set_quick_amount(amt)
            )
            btn.pack(side='left', padx=2)
        
        # زر المبلغ الصحيح
        exact_btn = ttk.Button(
            self.quick_amounts_frame,
            text="مبلغ صحيح",
            command=self._set_exact_amount
        )
        exact_btn.pack(side='left', padx=2)
        
        # الباقي
        self.change_frame = ttk.Frame(self.details_frame)
        self.change_frame.pack(fill='x', pady=5)
        
        ttk.Label(self.change_frame, text="الباقي:", font=("Tahoma", 11, "bold")).pack(side='right')
        self.change_label = ttk.Label(
            self.change_frame,
            text=f"0.00 {self.config.currency}",
            font=("Tahoma", 11, "bold"),
            foreground="#27AE60"
        )
        self.change_label.pack(side='left')
    
    def _create_control_buttons(self, parent):
        """إنشاء أزرار التحكم"""
        buttons_frame = ttk.Frame(parent)
        buttons_frame.pack(fill='x', pady=(20, 0))
        
        # زر الإلغاء
        cancel_btn = ttk.Button(
            buttons_frame,
            text="إلغاء",
            command=self._on_cancel,
            width=15
        )
        cancel_btn.pack(side='left')
        
        # زر التأكيد
        self.confirm_btn = ttk.Button(
            buttons_frame,
            text="تأكيد الدفع",
            command=self._on_confirm,
            style="Success.TButton",
            width=15
        )
        self.confirm_btn.pack(side='right')
        
        # زر الطباعة والدفع
        print_pay_btn = ttk.Button(
            buttons_frame,
            text="دفع وطباعة",
            command=self._on_pay_and_print,
            style="Primary.TButton",
            width=15
        )
        print_pay_btn.pack(side='right', padx=(0, 10))
    
    def _on_payment_method_changed(self):
        """معالج تغيير طريقة الدفع"""
        method = self.payment_method_var.get()
        
        if method == "cash":
            self.details_frame.config(text="تفاصيل الدفع النقدي")
            self.received_frame.pack(fill='x', pady=5)
            self.quick_amounts_frame.pack(fill='x', pady=10)
            self.change_frame.pack(fill='x', pady=5)
            self.received_entry.focus()
        else:
            self.details_frame.config(text="تفاصيل الدفع الإلكتروني")
            self.received_frame.pack_forget()
            self.quick_amounts_frame.pack_forget()
            self.change_frame.pack_forget()
            
            # تعيين المبلغ المستلم مساوياً للمطلوب
            self.received_amount_var.set(self.total_amount)
            self.change_amount_var.set(0.0)
    
    def _calculate_change(self, event=None):
        """حساب الباقي"""
        try:
            received = self.received_amount_var.get()
            change = received - self.total_amount
            self.change_amount_var.set(max(0, change))
            
            if hasattr(self, 'change_label'):
                if change < 0:
                    self.change_label.config(
                        text=f"ناقص {abs(change):.2f} {self.config.currency}",
                        foreground="#E74C3C"
                    )
                else:
                    self.change_label.config(
                        text=f"{change:.2f} {self.config.currency}",
                        foreground="#27AE60"
                    )
        except:
            pass
    
    def _set_quick_amount(self, amount: float):
        """تعيين مبلغ سريع"""
        self.received_amount_var.set(amount)
        self._calculate_change()
    
    def _set_exact_amount(self):
        """تعيين المبلغ الصحيح"""
        self.received_amount_var.set(self.total_amount)
        self._calculate_change()
    
    def _validate_payment(self) -> bool:
        """التحقق من صحة بيانات الدفع"""
        method = self.payment_method_var.get()
        
        if method == "cash":
            received = self.received_amount_var.get()
            if received < self.total_amount:
                messagebox.showerror("خطأ", "المبلغ المستلم أقل من المطلوب")
                return False
        
        return True
    
    def _on_confirm(self):
        """تأكيد الدفع"""
        if not self._validate_payment():
            return
        
        self.result = {
            'payment_method': self.payment_method_var.get(),
            'total_amount': self.total_amount,
            'received_amount': self.received_amount_var.get(),
            'change_amount': self.change_amount_var.get(),
            'customer_name': self.customer_name_var.get().strip(),
            'customer_phone': self.customer_phone_var.get().strip(),
            'print_receipt': False
        }
        
        self.dialog.destroy()
    
    def _on_pay_and_print(self):
        """الدفع والطباعة"""
        if not self._validate_payment():
            return
        
        self.result = {
            'payment_method': self.payment_method_var.get(),
            'total_amount': self.total_amount,
            'received_amount': self.received_amount_var.get(),
            'change_amount': self.change_amount_var.get(),
            'customer_name': self.customer_name_var.get().strip(),
            'customer_phone': self.customer_phone_var.get().strip(),
            'print_receipt': True
        }
        
        self.dialog.destroy()
    
    def _on_cancel(self):
        """إلغاء الدفع"""
        self.result = None
        self.dialog.destroy()


class QuickPaymentButtons:
    """أزرار الدفع السريع"""
    
    def __init__(self, parent, total_amount: float, config, callback):
        """تهيئة أزرار الدفع السريع"""
        self.parent = parent
        self.total_amount = total_amount
        self.config = config
        self.callback = callback
        
        self.frame = ttk.Frame(parent)
        self._create_buttons()
    
    def _create_buttons(self):
        """إنشاء الأزرار"""
        # زر الدفع النقدي السريع
        cash_btn = ttk.Button(
            self.frame,
            text=f"💵 نقدي\n{self.total_amount:.2f} {self.config.currency}",
            style="Success.TButton",
            command=lambda: self._quick_payment('cash')
        )
        cash_btn.pack(fill='x', pady=2)
        
        # زر الدفع بالبطاقة
        card_btn = ttk.Button(
            self.frame,
            text=f"💳 بطاقة\n{self.total_amount:.2f} {self.config.currency}",
            style="Success.TButton",
            command=lambda: self._quick_payment('card')
        )
        card_btn.pack(fill='x', pady=2)
        
        # زر الدفع الرقمي
        digital_btn = ttk.Button(
            self.frame,
            text=f"📱 رقمي\n{self.total_amount:.2f} {self.config.currency}",
            style="Success.TButton",
            command=lambda: self._quick_payment('digital')
        )
        digital_btn.pack(fill='x', pady=2)
    
    def _quick_payment(self, method: str):
        """الدفع السريع"""
        payment_data = {
            'payment_method': method,
            'total_amount': self.total_amount,
            'received_amount': self.total_amount,
            'change_amount': 0.0,
            'customer_name': '',
            'customer_phone': '',
            'print_receipt': True
        }
        
        self.callback(payment_data)
    
    def pack(self, **kwargs):
        """تخطيط الإطار"""
        return self.frame.pack(**kwargs)
    
    def update_amount(self, new_amount: float):
        """تحديث المبلغ"""
        self.total_amount = new_amount
        
        # إعادة إنشاء الأزرار
        for widget in self.frame.winfo_children():
            widget.destroy()
        
        self._create_buttons()