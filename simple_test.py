#!/usr/bin/env python3
# -*- coding: utf-8 -*-
"""
اختبار مبسط لنظام نقطة البيع
Simple test for POS System
"""

import tkinter as tk
from tkinter import ttk, messagebox
import sys
import os

# إضافة مسار المشروع
sys.path.append(os.path.dirname(os.path.abspath(__file__)))

class SimpleTest:
    def __init__(self):
        self.root = tk.Tk()
        self.root.title("اختبار نظام نقطة البيع")
        self.root.geometry("400x300")
        
        # توسيط النافذة
        self.center_window()
        
        # إنشاء الواجهة
        self.create_widgets()
    
    def center_window(self):
        """توسيط النافذة على الشاشة"""
        self.root.update_idletasks()
        width = self.root.winfo_width()
        height = self.root.winfo_height()
        x = (self.root.winfo_screenwidth() - width) // 2
        y = (self.root.winfo_screenheight() - height) // 2
        self.root.geometry(f"{width}x{height}+{x}+{y}")
    
    def create_widgets(self):
        """إنشاء عناصر الواجهة"""
        # العنوان
        title_label = ttk.Label(
            self.root,
            text="🍖 نظام نقطة البيع - شاورما الدجاج",
            font=("Tahoma", 14, "bold")
        )
        title_label.pack(pady=20)
        
        # رسالة الحالة
        status_label = ttk.Label(
            self.root,
            text="✅ النظام يعمل بشكل صحيح!",
            font=("Tahoma", 12),
            foreground="green"
        )
        status_label.pack(pady=10)
        
        # معلومات النظام
        info_frame = ttk.LabelFrame(self.root, text="معلومات النظام", padding="10")
        info_frame.pack(fill='x', padx=20, pady=10)
        
        ttk.Label(info_frame, text=f"إصدار Python: {sys.version_info.major}.{sys.version_info.minor}.{sys.version_info.micro}").pack(anchor='w')
        ttk.Label(info_frame, text=f"مسار المشروع: {os.getcwd()}").pack(anchor='w')
        
        # أزرار الاختبار
        buttons_frame = ttk.Frame(self.root)
        buttons_frame.pack(pady=20)
        
        ttk.Button(
            buttons_frame,
            text="اختبار قاعدة البيانات",
            command=self.test_database
        ).pack(side='left', padx=5)
        
        ttk.Button(
            buttons_frame,
            text="اختبار الإعدادات",
            command=self.test_config
        ).pack(side='left', padx=5)
        
        ttk.Button(
            buttons_frame,
            text="تشغيل النظام",
            command=self.run_main_system
        ).pack(side='left', padx=5)
        
        # زر الإغلاق
        ttk.Button(
            self.root,
            text="إغلاق",
            command=self.root.quit
        ).pack(pady=10)
    
    def test_database(self):
        """اختبار قاعدة البيانات"""
        try:
            from database.db_manager import DatabaseManager
            
            db = DatabaseManager()
            db.initialize_database()
            
            # اختبار تسجيل الدخول
            user = db.authenticate_user("admin", "admin123")
            if user:
                messagebox.showinfo("نجح", "✅ قاعدة البيانات تعمل بشكل صحيح!")
            else:
                messagebox.showwarning("تحذير", "⚠️ لم يتم العثور على المستخدم")
            
            db.close()
            
        except Exception as e:
            messagebox.showerror("خطأ", f"خطأ في قاعدة البيانات:\n{str(e)}")
    
    def test_config(self):
        """اختبار الإعدادات"""
        try:
            from utils.config import Config
            
            config = Config()
            
            info = f"""✅ الإعدادات تعمل بشكل صحيح!

اسم المطعم: {config.restaurant_name}
العملة: {config.currency}
معدل الضريبة: {config.tax_rate * 100:.1f}%
"""
            
            messagebox.showinfo("الإعدادات", info)
            
        except Exception as e:
            messagebox.showerror("خطأ", f"خطأ في الإعدادات:\n{str(e)}")
    
    def run_main_system(self):
        """تشغيل النظام الرئيسي"""
        try:
            # إغلاق النافذة الحالية
            self.root.destroy()
            
            # تشغيل النظام الرئيسي
            from main import ShawarmaChickenPOS
            app = ShawarmaChickenPOS()
            app.run()
            
        except Exception as e:
            # إعادة إنشاء نافذة الاختبار في حالة الخطأ
            messagebox.showerror("خطأ", f"خطأ في تشغيل النظام:\n{str(e)}")
            self.__init__()
    
    def run(self):
        """تشغيل الاختبار"""
        self.root.mainloop()

def main():
    """الدالة الرئيسية"""
    try:
        print("🧪 تشغيل اختبار مبسط للنظام...")
        
        app = SimpleTest()
        app.run()
        
        print("✅ تم الانتهاء من الاختبار")
        
    except Exception as e:
        print(f"❌ خطأ في الاختبار: {str(e)}")
        import traceback
        traceback.print_exc()

if __name__ == "__main__":
    main()