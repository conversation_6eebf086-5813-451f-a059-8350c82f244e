#!/usr/bin/env python3
# -*- coding: utf-8 -*-
"""
مكونات واجهة حديثة ومتقدمة
Modern and Advanced UI Components
"""

import tkinter as tk
from tkinter import ttk
from typing import Callable, Optional, Any, Dict, List
import math
from .theme_manager import theme_manager

class ModernButton(tk.Button):
    """زر حديث مع تأثيرات بصرية"""
    
    def __init__(self, parent, text="", command=None, button_type="default", 
                 icon=None, **kwargs):
        self.button_type = button_type
        self.icon = icon
        self.is_hovered = False
        
        super().__init__(parent, text=text, command=command, **kwargs)
        
        # تطبيق الثيم
        self._apply_theme()
        
        # ربط الأحداث
        self.bind("<Enter>", self._on_enter)
        self.bind("<Leave>", self._on_leave)
        self.bind("<Button-1>", self._on_click)
        self.bind("<ButtonRelease-1>", self._on_release)
    
    def _apply_theme(self):
        """تطبيق الثيم على الزر"""
        theme_manager.apply_theme_to_widget(self, self.button_type)
        
        # إعدادات إضافية
        self.configure(
            relief='flat',
            borderwidth=0,
            padx=20,
            pady=10,
            cursor='hand2'
        )
    
    def _on_enter(self, event):
        """عند دخول الماوس"""
        self.is_hovered = True
        colors = theme_manager.get_current_theme()['colors']
        
        if self.button_type == "primary":
            self.configure(bg=colors['primary_dark'])
        elif self.button_type == "success":
            self.configure(bg=colors['success'])
        elif self.button_type == "danger":
            self.configure(bg=colors['error'])
        else:
            self.configure(bg=colors['hover'])
    
    def _on_leave(self, event):
        """عند خروج الماوس"""
        self.is_hovered = False
        self._apply_theme()
    
    def _on_click(self, event):
        """عند الضغط"""
        self.configure(relief='sunken')
    
    def _on_release(self, event):
        """عند الإفلات"""
        self.configure(relief='flat')

class ModernCard(tk.Frame):
    """بطاقة حديثة مع ظل"""
    
    def __init__(self, parent, title="", **kwargs):
        super().__init__(parent, **kwargs)
        
        self.title = title
        self._create_widgets()
        self._apply_theme()
    
    def _create_widgets(self):
        """إنشاء عناصر البطاقة"""
        # إطار الظل
        self.shadow_frame = tk.Frame(self, height=2)
        self.shadow_frame.pack(fill='x', side='bottom')
        
        # إطار المحتوى
        self.content_frame = tk.Frame(self)
        self.content_frame.pack(fill='both', expand=True, padx=2, pady=2)
        
        if self.title:
            # عنوان البطاقة
            self.title_label = tk.Label(
                self.content_frame, 
                text=self.title,
                font=('Segoe UI', 12, 'bold')
            )
            self.title_label.pack(anchor='w', padx=15, pady=(15, 5))
            
            # خط فاصل
            self.separator = tk.Frame(self.content_frame, height=1)
            self.separator.pack(fill='x', padx=15, pady=(0, 10))
    
    def _apply_theme(self):
        """تطبيق الثيم"""
        colors = theme_manager.get_current_theme()['colors']
        
        self.configure(bg=colors['background'])
        self.shadow_frame.configure(bg=colors['shadow'])
        self.content_frame.configure(bg=colors['card'])
        
        if hasattr(self, 'title_label'):
            self.title_label.configure(
                bg=colors['card'],
                fg=colors['text_primary']
            )
        
        if hasattr(self, 'separator'):
            self.separator.configure(bg=colors['border'])

class ModernEntry(tk.Frame):
    """حقل إدخال حديث مع تسمية"""
    
    def __init__(self, parent, label="", placeholder="", **kwargs):
        super().__init__(parent)
        
        self.label_text = label
        self.placeholder_text = placeholder
        self.is_focused = False
        
        self._create_widgets()
        self._apply_theme()
        
        # ربط الأحداث
        self.entry.bind("<FocusIn>", self._on_focus_in)
        self.entry.bind("<FocusOut>", self._on_focus_out)
    
    def _create_widgets(self):
        """إنشاء عناصر الحقل"""
        if self.label_text:
            self.label = tk.Label(self, text=self.label_text)
            self.label.pack(anchor='w', pady=(0, 5))
        
        # إطار الحقل
        self.entry_frame = tk.Frame(self, relief='solid', borderwidth=1)
        self.entry_frame.pack(fill='x', pady=(0, 10))
        
        # حقل الإدخال
        self.entry = tk.Entry(self.entry_frame, relief='flat', borderwidth=0)
        self.entry.pack(fill='x', padx=10, pady=8)
        
        # إضافة placeholder
        if self.placeholder_text:
            self._show_placeholder()
    
    def _apply_theme(self):
        """تطبيق الثيم"""
        colors = theme_manager.get_current_theme()['colors']
        
        self.configure(bg=colors['surface'])
        
        if hasattr(self, 'label'):
            self.label.configure(
                bg=colors['surface'],
                fg=colors['text_secondary'],
                font=('Segoe UI', 9)
            )
        
        self.entry_frame.configure(bg=colors['border'])
        self.entry.configure(
            bg=colors['card'],
            fg=colors['text_primary'],
            font=('Segoe UI', 10)
        )
    
    def _show_placeholder(self):
        """عرض النص التوضيحي"""
        if not self.entry.get():
            self.entry.insert(0, self.placeholder_text)
            colors = theme_manager.get_current_theme()['colors']
            self.entry.configure(fg=colors['text_hint'])
    
    def _hide_placeholder(self):
        """إخفاء النص التوضيحي"""
        if self.entry.get() == self.placeholder_text:
            self.entry.delete(0, tk.END)
            colors = theme_manager.get_current_theme()['colors']
            self.entry.configure(fg=colors['text_primary'])
    
    def _on_focus_in(self, event):
        """عند التركيز"""
        self.is_focused = True
        colors = theme_manager.get_current_theme()['colors']
        self.entry_frame.configure(bg=colors['primary'])
        self._hide_placeholder()
    
    def _on_focus_out(self, event):
        """عند فقدان التركيز"""
        self.is_focused = False
        colors = theme_manager.get_current_theme()['colors']
        self.entry_frame.configure(bg=colors['border'])
        self._show_placeholder()
    
    def get(self):
        """الحصول على النص"""
        text = self.entry.get()
        return "" if text == self.placeholder_text else text
    
    def set(self, value):
        """تعيين النص"""
        self.entry.delete(0, tk.END)
        self.entry.insert(0, value)
        colors = theme_manager.get_current_theme()['colors']
        self.entry.configure(fg=colors['text_primary'])

class ModernProgressBar(tk.Frame):
    """شريط تقدم حديث"""
    
    def __init__(self, parent, width=200, height=6, **kwargs):
        super().__init__(parent, **kwargs)
        
        self.width = width
        self.height = height
        self.progress = 0.0
        
        self._create_widgets()
        self._apply_theme()
    
    def _create_widgets(self):
        """إنشاء شريط التقدم"""
        self.canvas = tk.Canvas(
            self, 
            width=self.width, 
            height=self.height,
            highlightthickness=0
        )
        self.canvas.pack()
        
        # الخلفية
        self.bg_rect = self.canvas.create_rectangle(
            0, 0, self.width, self.height,
            outline="", width=0
        )
        
        # شريط التقدم
        self.progress_rect = self.canvas.create_rectangle(
            0, 0, 0, self.height,
            outline="", width=0
        )
    
    def _apply_theme(self):
        """تطبيق الثيم"""
        colors = theme_manager.get_current_theme()['colors']
        
        self.configure(bg=colors['surface'])
        self.canvas.configure(bg=colors['surface'])
        
        # تحديث الألوان
        self.canvas.itemconfig(self.bg_rect, fill=colors['border'])
        self.canvas.itemconfig(self.progress_rect, fill=colors['primary'])
    
    def set_progress(self, value):
        """تعيين قيمة التقدم (0-100)"""
        self.progress = max(0, min(100, value))
        progress_width = (self.progress / 100) * self.width
        
        self.canvas.coords(
            self.progress_rect,
            0, 0, progress_width, self.height
        )

class ModernNotification(tk.Toplevel):
    """إشعار حديث"""
    
    def __init__(self, parent, message="", notification_type="info", duration=3000):
        super().__init__(parent)
        
        self.message = message
        self.notification_type = notification_type
        self.duration = duration
        
        self._setup_window()
        self._create_widgets()
        self._apply_theme()
        self._show_notification()
    
    def _setup_window(self):
        """إعداد النافذة"""
        self.overrideredirect(True)
        self.attributes('-topmost', True)
        
        # تحديد الموقع
        self.geometry("300x80")
        self._center_on_screen()
    
    def _center_on_screen(self):
        """توسيط الإشعار"""
        screen_width = self.winfo_screenwidth()
        screen_height = self.winfo_screenheight()
        
        x = screen_width - 320
        y = 50
        
        self.geometry(f"300x80+{x}+{y}")
    
    def _create_widgets(self):
        """إنشاء عناصر الإشعار"""
        # إطار رئيسي
        self.main_frame = tk.Frame(self, relief='solid', borderwidth=1)
        self.main_frame.pack(fill='both', expand=True, padx=2, pady=2)
        
        # أيقونة
        self.icon_label = tk.Label(
            self.main_frame,
            text=self._get_icon(),
            font=('Segoe UI', 16)
        )
        self.icon_label.pack(side='left', padx=15, pady=15)
        
        # النص
        self.message_label = tk.Label(
            self.main_frame,
            text=self.message,
            font=('Segoe UI', 10),
            wraplength=200,
            justify='right'
        )
        self.message_label.pack(side='right', padx=15, pady=15, fill='both', expand=True)
    
    def _get_icon(self):
        """الحصول على الأيقونة حسب النوع"""
        icons = {
            "success": "✅",
            "error": "❌", 
            "warning": "⚠️",
            "info": "ℹ️"
        }
        return icons.get(self.notification_type, "ℹ️")
    
    def _apply_theme(self):
        """تطبيق الثيم"""
        colors = theme_manager.get_current_theme()['colors']
        
        # ألوان حسب النوع
        type_colors = {
            "success": colors['success'],
            "error": colors['error'],
            "warning": colors['warning'],
            "info": colors['info']
        }
        
        bg_color = type_colors.get(self.notification_type, colors['info'])
        
        self.configure(bg=bg_color)
        self.main_frame.configure(bg=colors['card'], highlightbackground=bg_color)
        self.icon_label.configure(bg=colors['card'])
        self.message_label.configure(
            bg=colors['card'],
            fg=colors['text_primary']
        )
    
    def _show_notification(self):
        """عرض الإشعار مع تأثير"""
        # تأثير الظهور
        self.attributes('-alpha', 0.0)
        self._fade_in()
        
        # إخفاء تلقائي
        self.after(self.duration, self._fade_out)
    
    def _fade_in(self, alpha=0.0):
        """تأثير الظهور التدريجي"""
        alpha += 0.1
        if alpha <= 1.0:
            self.attributes('-alpha', alpha)
            self.after(50, lambda: self._fade_in(alpha))
    
    def _fade_out(self, alpha=1.0):
        """تأثير الاختفاء التدريجي"""
        alpha -= 0.1
        if alpha >= 0.0:
            self.attributes('-alpha', alpha)
            self.after(50, lambda: self._fade_out(alpha))
        else:
            self.destroy()

def show_notification(parent, message, notification_type="info", duration=3000):
    """عرض إشعار سريع"""
    return ModernNotification(parent, message, notification_type, duration)
