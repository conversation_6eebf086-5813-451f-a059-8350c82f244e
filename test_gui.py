#!/usr/bin/env python3
# -*- coding: utf-8 -*-
"""
اختبار بسيط لواجهة المستخدم الرسومية
Simple GUI Test
"""

import tkinter as tk
from tkinter import messagebox
import sys

def test_gui():
    """اختبار واجهة المستخدم الرسومية"""
    try:
        print("🧪 اختبار واجهة المستخدم الرسومية...")
        
        # إنشاء نافذة اختبار
        root = tk.Tk()
        root.title("اختبار واجهة المستخدم")
        root.geometry("400x300")
        
        # إضافة تسمية
        label = tk.Label(root, text="✅ واجهة المستخدم تعمل بشكل صحيح!", 
                        font=("Arial", 14), fg="green")
        label.pack(pady=50)
        
        # إضافة زر
        def on_button_click():
            messagebox.showinfo("نجح الاختبار", "واجهة المستخدم تعمل بشكل ممتاز!")
            root.quit()
        
        button = tk.Button(root, text="اختبار الرسائل", command=on_button_click,
                          font=("Arial", 12), bg="lightblue")
        button.pack(pady=20)
        
        # زر الإغلاق
        close_button = tk.Button(root, text="إغلاق", command=root.quit,
                               font=("Arial", 12), bg="lightcoral")
        close_button.pack(pady=10)
        
        print("✅ تم إنشاء النافذة بنجاح")
        print("📱 عرض النافذة...")
        
        # عرض النافذة
        root.mainloop()
        
        print("👋 تم إغلاق النافذة")
        return True
        
    except Exception as e:
        print(f"❌ خطأ في اختبار واجهة المستخدم: {str(e)}")
        return False

def main():
    """الدالة الرئيسية"""
    print("🧪 بدء اختبار النظام")
    print("=" * 40)
    
    if test_gui():
        print("\n✅ اختبار واجهة المستخدم نجح")
    else:
        print("\n❌ فشل اختبار واجهة المستخدم")
        print("💡 تأكد من تشغيل النظام في بيئة تدعم الواجهة الرسومية")

if __name__ == "__main__":
    main()
