#!/usr/bin/env python3
# -*- coding: utf-8 -*-
"""
مدير الطباعة - نظام نقطة البيع
Printer Manager for POS System
"""

import os
import tempfile
from datetime import datetime
from typing import Dict, List, Optional
import subprocess
import platform

class ThermalPrinter:
    """مدير الطابعة الحرارية"""
    
    def __init__(self, config):
        """تهيئة مدير الطباعة"""
        self.config = config
        self.paper_width = config.get('printer.paper_width', 80)
        self.printer_name = config.get('printer.printer_name', 'default')
        self.enabled = config.get('printer.enabled', True)
        self.auto_print = config.get('printer.auto_print', True)
        self.copies = config.get('printer.copies', 1)
        self.footer = config.get('printer.receipt_footer', 'شكراً لزيارتكم')
        
        # إعدادات الخط والتنسيق
        self.line_length = 32 if self.paper_width == 80 else 24
        self.char_width = 12 if self.paper_width == 80 else 18
    
    def print_receipt(self, order_data: Dict, payment_data: Dict = None) -> bool:
        """طباعة فاتورة الطلب"""
        if not self.enabled:
            return True
        
        try:
            receipt_content = self._generate_receipt_content(order_data, payment_data)
            return self._send_to_printer(receipt_content)
        
        except Exception as e:
            print(f"خطأ في طباعة الفاتورة: {e}")
            return False
    
    def print_kitchen_order(self, order_data: Dict) -> bool:
        """طباعة طلب المطبخ"""
        if not self.enabled:
            return True
        
        try:
            kitchen_content = self._generate_kitchen_order_content(order_data)
            return self._send_to_printer(kitchen_content)
        
        except Exception as e:
            print(f"خطأ في طباعة طلب المطبخ: {e}")
            return False
    
    def _generate_receipt_content(self, order_data: Dict, payment_data: Dict = None) -> str:
        """إنشاء محتوى الفاتورة"""
        lines = []
        
        # رأس الفاتورة
        lines.extend(self._generate_header())
        
        # معلومات الطلب
        lines.append(self._center_text("=" * self.line_length))
        lines.append(f"رقم الطلب: {order_data.get('order_number', 'N/A')}")
        lines.append(f"التاريخ: {datetime.now().strftime('%Y-%m-%d %H:%M')}")
        lines.append(f"الكاشير: {order_data.get('cashier_name', 'غير محدد')}")
        
        if order_data.get('table_number'):
            lines.append(f"رقم الطاولة: {order_data.get('table_number')}")
        
        if order_data.get('customer_name'):
            lines.append(f"اسم العميل: {order_data.get('customer_name')}")
        
        lines.append(self._center_text("=" * self.line_length))
        
        # عناصر الطلب
        lines.append(self._format_line("الصنف", "الكمية", "السعر"))
        lines.append("-" * self.line_length)
        
        total_items = 0
        subtotal = 0.0
        
        for item in order_data.get('items', []):
            item_name = item.get('name_ar', item.get('name', ''))
            quantity = item.get('quantity', 1)
            unit_price = item.get('unit_price', 0.0)
            total_price = item.get('total_price', quantity * unit_price)
            
            # اسم الصنف (قد يحتاج إلى تقسيم على أكثر من سطر)
            if len(item_name) > 20:
                lines.append(item_name[:20])
                lines.append(f"    {quantity} x {unit_price:.2f} = {total_price:.2f}")
            else:
                lines.append(self._format_item_line(item_name, quantity, unit_price, total_price))
            
            # ملاحظات خاصة
            if item.get('special_notes'):
                lines.append(f"  ملاحظة: {item['special_notes']}")
            
            total_items += quantity
            subtotal += total_price
        
        lines.append("-" * self.line_length)
        
        # الإجماليات
        lines.append(f"عدد الأصناف: {total_items}")
        lines.append(f"المجموع الفرعي: {subtotal:.2f} {self.config.currency}")
        
        # الخصم
        discount = order_data.get('discount_amount', 0.0)
        if discount > 0:
            lines.append(f"الخصم: -{discount:.2f} {self.config.currency}")
        
        # الضريبة
        tax = order_data.get('tax_amount', 0.0)
        if tax > 0:
            lines.append(f"ضريبة القيمة المضافة: {tax:.2f} {self.config.currency}")
        
        # الإجمالي النهائي
        total = order_data.get('total_amount', subtotal + tax - discount)
        lines.append("=" * self.line_length)
        lines.append(self._format_total_line(f"المجموع النهائي: {total:.2f} {self.config.currency}"))
        
        # معلومات الدفع
        if payment_data:
            lines.append("")
            lines.append("تفاصيل الدفع:")
            lines.append("-" * self.line_length)
            
            payment_method = payment_data.get('payment_method', 'cash')
            method_ar = {'cash': 'نقدي', 'card': 'بطاقة', 'digital': 'محفظة رقمية'}.get(payment_method, 'نقدي')
            lines.append(f"طريقة الدفع: {method_ar}")
            
            if payment_method == 'cash':
                received = payment_data.get('received_amount', total)
                change = payment_data.get('change_amount', 0.0)
                lines.append(f"المبلغ المستلم: {received:.2f} {self.config.currency}")
                if change > 0:
                    lines.append(f"الباقي: {change:.2f} {self.config.currency}")
        
        # ذيل الفاتورة
        lines.extend(self._generate_footer())
        
        return '\n'.join(lines)
    
    def _generate_kitchen_order_content(self, order_data: Dict) -> str:
        """إنشاء محتوى طلب المطبخ"""
        lines = []
        
        # رأس طلب المطبخ
        lines.append(self._center_text("=== طلب المطبخ ==="))
        lines.append("")
        lines.append(f"رقم الطلب: {order_data.get('order_number', 'N/A')}")
        lines.append(f"الوقت: {datetime.now().strftime('%H:%M:%S')}")
        
        if order_data.get('table_number'):
            lines.append(f"رقم الطاولة: {order_data.get('table_number')}")
        
        order_type = order_data.get('order_type', 'dine_in')
        type_ar = {'dine_in': 'في المطعم', 'takeaway': 'طلب خارجي', 'delivery': 'توصيل'}.get(order_type, 'في المطعم')
        lines.append(f"نوع الطلب: {type_ar}")
        
        lines.append("=" * self.line_length)
        
        # عناصر الطلب للمطبخ
        for item in order_data.get('items', []):
            item_name = item.get('name_ar', item.get('name', ''))
            quantity = item.get('quantity', 1)
            
            lines.append(f"[ ] {quantity} x {item_name}")
            
            if item.get('special_notes'):
                lines.append(f"    ملاحظة: {item['special_notes']}")
            
            lines.append("")
        
        # ملاحظات الطلب
        if order_data.get('notes'):
            lines.append("ملاحظات:")
            lines.append(order_data['notes'])
            lines.append("")
        
        lines.append("=" * self.line_length)
        lines.append(self._center_text("تم الطلب في: " + datetime.now().strftime('%H:%M')))
        
        return '\n'.join(lines)
    
    def _generate_header(self) -> List[str]:
        """إنشاء رأس الفاتورة"""
        lines = []
        
        # اسم المطعم
        restaurant_name = self.config.restaurant_name
        lines.append(self._center_text(restaurant_name))
        
        # معلومات الاتصال
        phone = self.config.restaurant_phone
        if phone:
            lines.append(self._center_text(f"هاتف: {phone}"))
        
        # رقم السجل التجاري ورقم ضريبة القيمة المضافة
        cr_number = self.config.get('restaurant.cr_number')
        if cr_number:
            lines.append(self._center_text(f"س.ت: {cr_number}"))
        
        vat_number = self.config.get('restaurant.vat_number')
        if vat_number:
            lines.append(self._center_text(f"الرقم الضريبي: {vat_number}"))
        
        lines.append("")
        return lines
    
    def _generate_footer(self) -> List[str]:
        """إنشاء ذيل الفاتورة"""
        lines = []
        lines.append("")
        lines.append("=" * self.line_length)
        lines.append(self._center_text(self.footer))
        lines.append(self._center_text("تطبيق نقطة البيع"))
        lines.append("")
        
        # كود QR أو باركود (يمكن إضافته لاحقاً)
        
        # مساحة للقطع
        lines.extend([""] * 3)
        
        return lines
    
    def _center_text(self, text: str) -> str:
        """توسيط النص"""
        if len(text) >= self.line_length:
            return text[:self.line_length]
        
        padding = (self.line_length - len(text)) // 2
        return " " * padding + text
    
    def _format_line(self, col1: str, col2: str, col3: str) -> str:
        """تنسيق سطر بثلاثة أعمدة"""
        col1_width = 12
        col2_width = 6
        col3_width = self.line_length - col1_width - col2_width - 2
        
        col1 = col1[:col1_width].ljust(col1_width)
        col2 = col2[:col2_width].center(col2_width)
        col3 = col3[:col3_width].rjust(col3_width)
        
        return f"{col1} {col2} {col3}"
    
    def _format_item_line(self, name: str, quantity: int, unit_price: float, total_price: float) -> str:
        """تنسيق سطر الصنف"""
        name = name[:15]
        line = f"{name:<15}"
        line += f" {quantity}x{unit_price:.1f}={total_price:.2f}".rjust(self.line_length - 15)
        return line
    
    def _format_total_line(self, text: str) -> str:
        """تنسيق سطر الإجمالي"""
        return self._center_text(text)
    
    def _send_to_printer(self, content: str) -> bool:
        """إرسال المحتوى إلى الطابعة"""
        try:
            # في بيئة Windows
            if platform.system() == 'Windows':
                return self._print_windows(content)
            
            # في بيئة Linux/Unix
            else:
                return self._print_unix(content)
        
        except Exception as e:
            print(f"خطأ في الطباعة: {e}")
            return False
    
    def _print_windows(self, content: str) -> bool:
        """طباعة في نظام Windows"""
        try:
            # إنشاء ملف مؤقت
            with tempfile.NamedTemporaryFile(mode='w', suffix='.txt', delete=False, encoding='utf-8') as temp_file:
                temp_file.write(content)
                temp_file_path = temp_file.name
            
            # طباعة الملف باستخدام الأمر print في Windows
            if self.printer_name == 'default':
                subprocess.run(['print', temp_file_path], shell=True, check=True)
            else:
                subprocess.run(['print', f'/D:{self.printer_name}', temp_file_path], shell=True, check=True)
            
            # حذف الملف المؤقت
            os.unlink(temp_file_path)
            
            return True
        
        except subprocess.CalledProcessError:
            return False
        except Exception:
            return False
    
    def _print_unix(self, content: str) -> bool:
        """طباعة في نظام Unix/Linux"""
        try:
            # استخدام lpr للطباعة
            if self.printer_name == 'default':
                process = subprocess.Popen(['lpr'], stdin=subprocess.PIPE, text=True)
            else:
                process = subprocess.Popen(['lpr', '-P', self.printer_name], stdin=subprocess.PIPE, text=True)
            
            process.communicate(input=content)
            return process.returncode == 0
        
        except FileNotFoundError:
            # محاولة استخدام lp
            try:
                if self.printer_name == 'default':
                    process = subprocess.Popen(['lp'], stdin=subprocess.PIPE, text=True)
                else:
                    process = subprocess.Popen(['lp', '-d', self.printer_name], stdin=subprocess.PIPE, text=True)
                
                process.communicate(input=content)
                return process.returncode == 0
            
            except FileNotFoundError:
                return False
        
        except Exception:
            return False
    
    def test_printer(self) -> bool:
        """اختبار الطابعة"""
        test_content = self._generate_test_receipt()
        return self._send_to_printer(test_content)
    
    def _generate_test_receipt(self) -> str:
        """إنشاء فاتورة اختبار"""
        lines = []
        lines.append(self._center_text("=== اختبار الطابعة ==="))
        lines.append("")
        lines.append(self._center_text(self.config.restaurant_name))
        lines.append("")
        lines.append(f"التاريخ: {datetime.now().strftime('%Y-%m-%d %H:%M:%S')}")
        lines.append("")
        lines.append("تم اختبار الطابعة بنجاح!")
        lines.append("")
        lines.append("=" * self.line_length)
        lines.append("")
        
        return '\n'.join(lines)