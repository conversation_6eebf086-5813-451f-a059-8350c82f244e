# متطلبات نظام نقطة البيع - شاورما الدجاج
# POS System Requirements - Shawarma Chicken

# Core Python libraries (usually included with Python)
tkinter>=8.6  # GUI framework
sqlite3  # Database
hashlib  # Password hashing
datetime  # Date and time operations
json  # JSON operations
threading  # Multi-threading support
logging  # Logging system

# Optional libraries for enhanced functionality
# يمكن تثبيت هذه المكتبات لمزيد من الوظائف

# For advanced printing (optional)
# pip install pywin32  # Windows printing support
# pip install cups-python  # Linux/Unix printing support

# For Excel export (optional)
# pip install openpyxl  # Excel file operations
# pip install xlsxwriter  # Excel writing

# For PDF reports (optional)
# pip install reportlab  # PDF generation
# pip install matplotlib  # Charts and graphs

# For barcode/QR code generation (optional)
# pip install python-barcode  # Barcode generation
# pip install qrcode  # QR code generation
# pip install Pillow  # Image processing

# For advanced database features (optional)
# pip install sqlalchemy  # Advanced database ORM

# For network features (optional)
# pip install requests  # HTTP requests
# pip install flask  # Web server (for remote access)

# Development and testing (optional)
# pip install pytest  # Testing framework
# pip install black  # Code formatting
# pip install flake8  # Code linting