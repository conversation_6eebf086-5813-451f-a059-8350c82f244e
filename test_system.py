#!/usr/bin/env python3
# -*- coding: utf-8 -*-
"""
اختبار النظام - نظام نقطة البيع
System Test for POS System
"""

import sys
import os
import traceback

def test_imports():
    """اختبار استيراد المكتبات"""
    print("اختبار استيراد المكتبات...")
    
    try:
        import tkinter as tk
        print("✅ tkinter - متوفر")
    except ImportError:
        print("❌ tkinter - غير متوفر")
        return False
    
    try:
        import sqlite3
        print("✅ sqlite3 - متوفر")
    except ImportError:
        print("❌ sqlite3 - غير متوفر")
        return False
    
    try:
        import hashlib
        print("✅ hashlib - متوفر")
    except ImportError:
        print("❌ hashlib - غير متوفر")
        return False
    
    try:
        import json
        print("✅ json - متوفر")
    except ImportError:
        print("❌ json - غير متوفر")
        return False
    
    try:
        import datetime
        print("✅ datetime - متوفر")
    except ImportError:
        print("❌ datetime - غير متوفر")
        return False
    
    try:
        import threading
        print("✅ threading - متوفر")
    except ImportError:
        print("❌ threading - غير متوفر")
        return False
    
    return True

def test_files():
    """اختبار وجود الملفات الأساسية"""
    print("\nاختبار وجود الملفات...")
    
    required_files = [
        'main.py',
        'config.json',
        'database/__init__.py',
        'database/db_manager.py',
        'ui/__init__.py',
        'ui/login_window.py',
        'ui/main_window.py',
        'ui/payment_dialog.py',
        'utils/__init__.py',
        'utils/config.py',
        'utils/printer.py',
        'reports/__init__.py',
        'reports/sales_report.py'
    ]
    
    missing_files = []
    
    for file_path in required_files:
        if os.path.exists(file_path):
            print(f"✅ {file_path}")
        else:
            print(f"❌ {file_path}")
            missing_files.append(file_path)
    
    return len(missing_files) == 0

def test_database():
    """اختبار قاعدة البيانات"""
    print("\nاختبار قاعدة البيانات...")
    
    try:
        # إضافة مسار النظام
        sys.path.append(os.path.dirname(os.path.abspath(__file__)))
        
        from database.db_manager import DatabaseManager
        
        # إنشاء قاعدة بيانات اختبار
        db = DatabaseManager("test_database.db")
        db.initialize_database()
        
        # اختبار المستخدمين
        user = db.authenticate_user("admin", "admin123")
        if user:
            print("✅ تسجيل الدخول - يعمل")
        else:
            print("❌ تسجيل الدخول - لا يعمل")
            return False
        
        # اختبار الفئات
        categories = db.get_categories()
        if categories:
            print(f"✅ الفئات - تم تحميل {len(categories)} فئة")
        else:
            print("❌ الفئات - لا توجد فئات")
        
        # اختبار عناصر القائمة
        menu_items = db.get_menu_items()
        if menu_items:
            print(f"✅ عناصر القائمة - تم تحميل {len(menu_items)} صنف")
        else:
            print("❌ عناصر القائمة - لا توجد أصناف")
        
        # اختبار الطاولات
        tables = db.get_available_tables()
        if tables:
            print(f"✅ الطاولات - تم تحميل {len(tables)} طاولة")
        else:
            print("❌ الطاولات - لا توجد طاولات")
        
        # إغلاق قاعدة البيانات
        db.close()
        
        # حذف قاعدة البيانات الاختباريه
        if os.path.exists("test_database.db"):
            os.remove("test_database.db")
        
        print("✅ قاعدة البيانات - تعمل بشكل صحيح")
        return True
        
    except Exception as e:
        print(f"❌ قاعدة البيانات - خطأ: {str(e)}")
        traceback.print_exc()
        return False

def test_config():
    """اختبار الإعدادات"""
    print("\nاختبار الإعدادات...")
    
    try:
        sys.path.append(os.path.dirname(os.path.abspath(__file__)))
        
        from utils.config import Config
        
        config = Config()
        
        # اختبار بعض الإعدادات
        app_name = config.app_name
        if app_name:
            print(f"✅ اسم التطبيق: {app_name}")
        else:
            print("❌ اسم التطبيق غير محدد")
            return False
        
        currency = config.currency
        if currency:
            print(f"✅ العملة: {currency}")
        else:
            print("❌ العملة غير محددة")
            return False
        
        tax_rate = config.tax_rate
        if tax_rate >= 0:
            print(f"✅ معدل الضريبة: {tax_rate * 100:.1f}%")
        else:
            print("❌ معدل الضريبة غير صحيح")
            return False
        
        print("✅ الإعدادات - تعمل بشكل صحيح")
        return True
        
    except Exception as e:
        print(f"❌ الإعدادات - خطأ: {str(e)}")
        traceback.print_exc()
        return False

def test_gui():
    """اختبار الواجهة الرسومية"""
    print("\nاختبار الواجهة الرسومية...")
    
    try:
        import tkinter as tk
        from tkinter import ttk
        
        # إنشاء نافذة اختبار
        root = tk.Tk()
        root.title("اختبار الواجهة")
        root.geometry("300x200")
        
        # إضافة عناصر اختبار
        label = ttk.Label(root, text="✅ الواجهة الرسومية تعمل")
        label.pack(pady=20)
        
        button = ttk.Button(root, text="إغلاق", command=root.destroy)
        button.pack(pady=10)
        
        # عرض النافذة لثانية واحدة
        root.after(1000, root.destroy)
        root.mainloop()
        
        print("✅ الواجهة الرسومية - تعمل بشكل صحيح")
        return True
        
    except Exception as e:
        print(f"❌ الواجهة الرسومية - خطأ: {str(e)}")
        traceback.print_exc()
        return False

def main():
    """الدالة الرئيسية للاختبار"""
    print("=" * 50)
    print("🧪 اختبار نظام نقطة البيع - شاورما الدجاج")
    print("=" * 50)
    
    all_tests_passed = True
    
    # تشغيل الاختبارات
    tests = [
        ("استيراد المكتبات", test_imports),
        ("وجود الملفات", test_files),
        ("قاعدة البيانات", test_database),
        ("الإعدادات", test_config),
        ("الواجهة الرسومية", test_gui)
    ]
    
    for test_name, test_func in tests:
        try:
            if not test_func():
                all_tests_passed = False
        except Exception as e:
            print(f"❌ {test_name} - خطأ غير متوقع: {str(e)}")
            all_tests_passed = False
    
    print("\n" + "=" * 50)
    if all_tests_passed:
        print("🎉 جميع الاختبارات نجحت! النظام جاهز للتشغيل.")
        print("📌 يمكنك الآن تشغيل النظام باستخدام: python main.py")
    else:
        print("⚠️  بعض الاختبارات فشلت. يرجى مراجعة الأخطاء أعلاه.")
        print("📞 للحصول على الدعم، تواصل معنا على: <EMAIL>")
    
    print("=" * 50)

if __name__ == "__main__":
    main()