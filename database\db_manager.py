#!/usr/bin/env python3
# -*- coding: utf-8 -*-
"""
مدير قاعدة البيانات - نظام نقطة البيع
Database Manager for POS System
"""

import sqlite3
import os
import hashlib
from datetime import datetime
from typing import List, Dict, Optional, Tuple
import logging

# إعداد السجلات
logging.basicConfig(level=logging.INFO)
logger = logging.getLogger(__name__)

class DatabaseManager:
    """مدير قاعدة البيانات الرئيسي"""
    
    def __init__(self, db_path: str = "pos_database.db"):
        """تهيئة مدير قاعدة البيانات"""
        self.db_path = db_path
        self.connection = None
        self.cursor = None
        
    def connect(self):
        """الاتصال بقاعدة البيانات"""
        try:
            self.connection = sqlite3.connect(
                self.db_path,
                check_same_thread=False,
                timeout=30.0
            )
            self.connection.row_factory = sqlite3.Row
            self.cursor = self.connection.cursor()
            
            # تفعيل المفاتيح الخارجية
            self.cursor.execute("PRAGMA foreign_keys = ON")
            self.connection.commit()
            
            logger.info("تم الاتصال بقاعدة البيانات بنجاح")
            return True
            
        except sqlite3.Error as e:
            logger.error(f"خطأ في الاتصال بقاعدة البيانات: {e}")
            return False
    
    def close(self):
        """إغلاق الاتصال بقاعدة البيانات"""
        try:
            if self.cursor:
                self.cursor.close()
            if self.connection:
                self.connection.close()
            logger.info("تم إغلاق اتصال قاعدة البيانات")
        except sqlite3.Error as e:
            logger.error(f"خطأ في إغلاق قاعدة البيانات: {e}")
    
    def initialize_database(self):
        """تهيئة جداول قاعدة البيانات"""
        if not self.connect():
            raise Exception("فشل في الاتصال بقاعدة البيانات")
        
        try:
            # إنشاء الجداول
            self._create_users_table()
            self._create_categories_table()
            self._create_menu_items_table()
            self._create_tables_table()
            self._create_orders_table()
            self._create_order_items_table()
            self._create_payments_table()
            self._create_inventory_table()
            self._create_reports_table()
            
            # إدراج البيانات الافتراضية
            self._insert_default_data()
            
            logger.info("تم تهيئة قاعدة البيانات بنجاح")
            
        except sqlite3.Error as e:
            logger.error(f"خطأ في تهيئة قاعدة البيانات: {e}")
            raise
    
    def _create_users_table(self):
        """إنشاء جدول المستخدمين"""
        query = '''
        CREATE TABLE IF NOT EXISTS users (
            id INTEGER PRIMARY KEY AUTOINCREMENT,
            username TEXT UNIQUE NOT NULL,
            password_hash TEXT NOT NULL,
            full_name TEXT NOT NULL,
            role TEXT NOT NULL CHECK (role IN ('admin', 'cashier', 'manager')),
            phone TEXT,
            email TEXT,
            is_active INTEGER DEFAULT 1,
            created_at TIMESTAMP DEFAULT CURRENT_TIMESTAMP,
            last_login TIMESTAMP
        )
        '''
        self.cursor.execute(query)
        self.connection.commit()
    
    def _create_categories_table(self):
        """إنشاء جدول الفئات"""
        query = '''
        CREATE TABLE IF NOT EXISTS categories (
            id INTEGER PRIMARY KEY AUTOINCREMENT,
            name TEXT NOT NULL,
            name_ar TEXT NOT NULL,
            description TEXT,
            is_active INTEGER DEFAULT 1,
            created_at TIMESTAMP DEFAULT CURRENT_TIMESTAMP
        )
        '''
        self.cursor.execute(query)
        self.connection.commit()
    
    def _create_menu_items_table(self):
        """إنشاء جدول عناصر القائمة"""
        query = '''
        CREATE TABLE IF NOT EXISTS menu_items (
            id INTEGER PRIMARY KEY AUTOINCREMENT,
            name TEXT NOT NULL,
            name_ar TEXT NOT NULL,
            description TEXT,
            description_ar TEXT,
            category_id INTEGER,
            price REAL NOT NULL,
            cost REAL DEFAULT 0,
            image_path TEXT,
            is_available INTEGER DEFAULT 1,
            is_active INTEGER DEFAULT 1,
            preparation_time INTEGER DEFAULT 5,
            calories INTEGER,
            created_at TIMESTAMP DEFAULT CURRENT_TIMESTAMP,
            FOREIGN KEY (category_id) REFERENCES categories (id)
        )
        '''
        self.cursor.execute(query)
        self.connection.commit()
    
    def _create_tables_table(self):
        """إنشاء جدول الطاولات"""
        query = '''
        CREATE TABLE IF NOT EXISTS restaurant_tables (
            id INTEGER PRIMARY KEY AUTOINCREMENT,
            table_number INTEGER UNIQUE NOT NULL,
            capacity INTEGER NOT NULL,
            location TEXT,
            is_available INTEGER DEFAULT 1,
            is_active INTEGER DEFAULT 1,
            created_at TIMESTAMP DEFAULT CURRENT_TIMESTAMP
        )
        '''
        self.cursor.execute(query)
        self.connection.commit()
    
    def _create_orders_table(self):
        """إنشاء جدول الطلبات"""
        query = '''
        CREATE TABLE IF NOT EXISTS orders (
            id INTEGER PRIMARY KEY AUTOINCREMENT,
            order_number TEXT UNIQUE NOT NULL,
            table_id INTEGER,
            customer_name TEXT,
            customer_phone TEXT,
            order_type TEXT NOT NULL CHECK (order_type IN ('dine_in', 'takeaway', 'delivery')),
            status TEXT NOT NULL DEFAULT 'pending' CHECK (status IN ('pending', 'preparing', 'ready', 'served', 'cancelled')),
            subtotal REAL NOT NULL DEFAULT 0,
            tax_amount REAL NOT NULL DEFAULT 0,
            discount_amount REAL NOT NULL DEFAULT 0,
            total_amount REAL NOT NULL DEFAULT 0,
            notes TEXT,
            user_id INTEGER NOT NULL,
            created_at TIMESTAMP DEFAULT CURRENT_TIMESTAMP,
            updated_at TIMESTAMP DEFAULT CURRENT_TIMESTAMP,
            FOREIGN KEY (table_id) REFERENCES restaurant_tables (id),
            FOREIGN KEY (user_id) REFERENCES users (id)
        )
        '''
        self.cursor.execute(query)
        self.connection.commit()
    
    def _create_order_items_table(self):
        """إنشاء جدول عناصر الطلبات"""
        query = '''
        CREATE TABLE IF NOT EXISTS order_items (
            id INTEGER PRIMARY KEY AUTOINCREMENT,
            order_id INTEGER NOT NULL,
            menu_item_id INTEGER NOT NULL,
            quantity INTEGER NOT NULL DEFAULT 1,
            unit_price REAL NOT NULL,
            total_price REAL NOT NULL,
            special_notes TEXT,
            created_at TIMESTAMP DEFAULT CURRENT_TIMESTAMP,
            FOREIGN KEY (order_id) REFERENCES orders (id) ON DELETE CASCADE,
            FOREIGN KEY (menu_item_id) REFERENCES menu_items (id)
        )
        '''
        self.cursor.execute(query)
        self.connection.commit()
    
    def _create_payments_table(self):
        """إنشاء جدول المدفوعات"""
        query = '''
        CREATE TABLE IF NOT EXISTS payments (
            id INTEGER PRIMARY KEY AUTOINCREMENT,
            order_id INTEGER NOT NULL,
            payment_method TEXT NOT NULL CHECK (payment_method IN ('cash', 'card', 'digital')),
            amount REAL NOT NULL,
            received_amount REAL,
            change_amount REAL DEFAULT 0,
            transaction_id TEXT,
            status TEXT NOT NULL DEFAULT 'completed' CHECK (status IN ('pending', 'completed', 'failed', 'refunded')),
            notes TEXT,
            created_at TIMESTAMP DEFAULT CURRENT_TIMESTAMP,
            FOREIGN KEY (order_id) REFERENCES orders (id)
        )
        '''
        self.cursor.execute(query)
        self.connection.commit()
    
    def _create_inventory_table(self):
        """إنشاء جدول المخزون"""
        query = '''
        CREATE TABLE IF NOT EXISTS inventory (
            id INTEGER PRIMARY KEY AUTOINCREMENT,
            item_name TEXT NOT NULL,
            item_name_ar TEXT NOT NULL,
            current_stock REAL NOT NULL DEFAULT 0,
            minimum_stock REAL NOT NULL DEFAULT 0,
            unit TEXT NOT NULL,
            cost_per_unit REAL NOT NULL DEFAULT 0,
            supplier TEXT,
            last_updated TIMESTAMP DEFAULT CURRENT_TIMESTAMP,
            is_active INTEGER DEFAULT 1
        )
        '''
        self.cursor.execute(query)
        self.connection.commit()
    
    def _create_reports_table(self):
        """إنشاء جدول التقارير"""
        query = '''
        CREATE TABLE IF NOT EXISTS daily_reports (
            id INTEGER PRIMARY KEY AUTOINCREMENT,
            report_date DATE NOT NULL,
            total_orders INTEGER DEFAULT 0,
            total_sales REAL DEFAULT 0,
            total_cash REAL DEFAULT 0,
            total_card REAL DEFAULT 0,
            total_digital REAL DEFAULT 0,
            total_discount REAL DEFAULT 0,
            total_tax REAL DEFAULT 0,
            top_selling_item TEXT,
            notes TEXT,
            created_at TIMESTAMP DEFAULT CURRENT_TIMESTAMP,
            UNIQUE(report_date)
        )
        '''
        self.cursor.execute(query)
        self.connection.commit()
    
    def _insert_default_data(self):
        """إدراج البيانات الافتراضية"""
        try:
            # التحقق من وجود المستخدمين
            self.cursor.execute("SELECT COUNT(*) FROM users")
            user_count = self.cursor.fetchone()[0]
            
            if user_count == 0:
                # إنشاء المستخدم الافتراضي (المدير)
                admin_password = self._hash_password("admin123")
                self.cursor.execute('''
                    INSERT INTO users (username, password_hash, full_name, role, phone, email)
                    VALUES (?, ?, ?, ?, ?, ?)
                ''', ('admin', admin_password, 'مدير النظام', 'admin', '01234567890', '<EMAIL>'))
                
                # إنشاء كاشير افتراضي
                cashier_password = self._hash_password("cashier123")
                self.cursor.execute('''
                    INSERT INTO users (username, password_hash, full_name, role, phone, email)
                    VALUES (?, ?, ?, ?, ?, ?)
                ''', ('cashier', cashier_password, 'محمد أحمد', 'cashier', '01234567891', '<EMAIL>'))
            
            # التحقق من وجود الفئات
            self.cursor.execute("SELECT COUNT(*) FROM categories")
            category_count = self.cursor.fetchone()[0]
            
            if category_count == 0:
                categories = [
                    ('Shawarma', 'شاورما', 'شاورما بأنواعها المختلفة'),
                    ('Chicken', 'دجاج', 'أطباق الدجاج المشوي والمقلي'),
                    ('Sides', 'مقبلات', 'المقبلات والأطباق الجانبية'),
                    ('Beverages', 'مشروبات', 'المشروبات الساخنة والباردة'),
                    ('Desserts', 'حلويات', 'الحلويات والعصائر')
                ]
                
                for category in categories:
                    self.cursor.execute('''
                        INSERT INTO categories (name, name_ar, description)
                        VALUES (?, ?, ?)
                    ''', category)
            
            # التحقق من وجود عناصر القائمة
            self.cursor.execute("SELECT COUNT(*) FROM menu_items")
            menu_count = self.cursor.fetchone()[0]
            
            if menu_count == 0:
                menu_items = [
                    # شاورما
                    ('Chicken Shawarma', 'شاورما دجاج', 'شاورما دجاج طازجة مع الخضار والصلصة', 'شاورما دجاج طازجة مع الخضار والصلصة', 1, 25.00, 15.00),
                    ('Meat Shawarma', 'شاورما لحمة', 'شاورما لحمة طازجة مع الخضار والصلصة', 'شاورما لحمة طازجة مع الخضار والصلصة', 1, 30.00, 20.00),
                    ('Mixed Shawarma', 'شاورما مشكلة', 'شاورما مشكلة دجاج ولحمة', 'شاورما مشكلة دجاج ولحمة', 1, 35.00, 25.00),
                    
                    # دجاج
                    ('Grilled Chicken', 'دجاج مشوي', 'دجاج مشوي على الفحم', 'دجاج مشوي على الفحم', 2, 40.00, 25.00),
                    ('Fried Chicken', 'دجاج مقلي', 'دجاج مقلي مقرمش', 'دجاج مقلي مقرمش', 2, 35.00, 20.00),
                    ('Chicken Wings', 'أجنحة دجاج', 'أجنحة دجاج مشوية ومتبلة', 'أجنحة دجاج مشوية ومتبلة', 2, 28.00, 18.00),
                    
                    # مقبلات
                    ('Hummus', 'حمص', 'حمص بالطحينة والزيت', 'حمص بالطحينة والزيت', 3, 12.00, 6.00),
                    ('Tabbouleh', 'تبولة', 'سلطة تبولة طازجة', 'سلطة تبولة طازجة', 3, 15.00, 8.00),
                    ('French Fries', 'بطاطس مقلية', 'بطاطس مقلية مقرمشة', 'بطاطس مقلية مقرمشة', 3, 10.00, 5.00),
                    
                    # مشروبات
                    ('Soft Drink', 'مشروب غازي', 'مشروبات غازية متنوعة', 'مشروبات غازية متنوعة', 4, 8.00, 3.00),
                    ('Fresh Juice', 'عصير طبيعي', 'عصائر طبيعية طازجة', 'عصائر طبيعية طازجة', 4, 15.00, 8.00),
                    ('Arabic Tea', 'شاي عربي', 'شاي عربي أصيل', 'شاي عربي أصيل', 4, 5.00, 2.00),
                    
                    # حلويات
                    ('Baklava', 'بقلاوة', 'بقلاوة بالعسل والمكسرات', 'بقلاوة بالعسل والمكسرات', 5, 18.00, 10.00),
                    ('Kunafa', 'كنافة', 'كنافة بالجبن والقطر', 'كنافة بالجبن والقطر', 5, 20.00, 12.00)
                ]
                
                for item in menu_items:
                    self.cursor.execute('''
                        INSERT INTO menu_items (name, name_ar, description, description_ar, category_id, price, cost)
                        VALUES (?, ?, ?, ?, ?, ?, ?)
                    ''', item)
            
            # إنشاء طاولات افتراضية
            self.cursor.execute("SELECT COUNT(*) FROM restaurant_tables")
            table_count = self.cursor.fetchone()[0]
            
            if table_count == 0:
                for i in range(1, 21):  # 20 طاولة
                    capacity = 4 if i <= 15 else 6  # 15 طاولة لـ4 أشخاص، 5 طاولات لـ6 أشخاص
                    location = f"الطابق الأول" if i <= 10 else "الطابق الثاني"
                    
                    self.cursor.execute('''
                        INSERT INTO restaurant_tables (table_number, capacity, location)
                        VALUES (?, ?, ?)
                    ''', (i, capacity, location))
            
            # إدراج بيانات المخزون الافتراضية
            self.cursor.execute("SELECT COUNT(*) FROM inventory")
            inventory_count = self.cursor.fetchone()[0]
            
            if inventory_count == 0:
                inventory_items = [
                    ('Chicken Breast', 'صدور دجاج', 50.0, 10.0, 'kg', 25.00),
                    ('Beef', 'لحم بقري', 30.0, 5.0, 'kg', 45.00),
                    ('Pita Bread', 'خبز عربي', 200.0, 50.0, 'piece', 0.50),
                    ('Tomatoes', 'طماطم', 20.0, 5.0, 'kg', 3.00),
                    ('Onions', 'بصل', 15.0, 3.0, 'kg', 2.50),
                    ('Lettuce', 'خس', 10.0, 2.0, 'kg', 4.00),
                    ('Tahini', 'طحينة', 5.0, 1.0, 'kg', 15.00),
                    ('Garlic Sauce', 'صلصة الثوم', 3.0, 0.5, 'liter', 20.00)
                ]
                
                for item in inventory_items:
                    self.cursor.execute('''
                        INSERT INTO inventory (item_name, item_name_ar, current_stock, minimum_stock, unit, cost_per_unit)
                        VALUES (?, ?, ?, ?, ?, ?)
                    ''', item)
            
            self.connection.commit()
            logger.info("تم إدراج البيانات الافتراضية بنجاح")
            
        except sqlite3.Error as e:
            logger.error(f"خطأ في إدراج البيانات الافتراضية: {e}")
            self.connection.rollback()
            raise
    
    def _hash_password(self, password: str) -> str:
        """تشفير كلمة المرور"""
        return hashlib.sha256(password.encode()).hexdigest()
    
    def authenticate_user(self, username: str, password: str) -> Optional[Dict]:
        """التحقق من صحة بيانات المستخدم"""
        try:
            password_hash = self._hash_password(password)
            
            self.cursor.execute('''
                SELECT id, username, full_name, role, phone, email, is_active
                FROM users 
                WHERE username = ? AND password_hash = ? AND is_active = 1
            ''', (username, password_hash))
            
            user = self.cursor.fetchone()
            
            if user:
                # تحديث آخر تسجيل دخول
                self.cursor.execute('''
                    UPDATE users SET last_login = CURRENT_TIMESTAMP WHERE id = ?
                ''', (user['id'],))
                self.connection.commit()
                
                return dict(user)
            
            return None
            
        except sqlite3.Error as e:
            logger.error(f"خطأ في التحقق من المستخدم: {e}")
            return None
    
    def get_menu_items(self, category_id: Optional[int] = None) -> List[Dict]:
        """الحصول على عناصر القائمة"""
        try:
            if category_id:
                self.cursor.execute('''
                    SELECT m.*, c.name_ar as category_name
                    FROM menu_items m
                    LEFT JOIN categories c ON m.category_id = c.id
                    WHERE m.category_id = ? AND m.is_active = 1 AND m.is_available = 1
                    ORDER BY m.name_ar
                ''', (category_id,))
            else:
                self.cursor.execute('''
                    SELECT m.*, c.name_ar as category_name
                    FROM menu_items m
                    LEFT JOIN categories c ON m.category_id = c.id
                    WHERE m.is_active = 1 AND m.is_available = 1
                    ORDER BY c.name_ar, m.name_ar
                ''')
            
            return [dict(row) for row in self.cursor.fetchall()]
            
        except sqlite3.Error as e:
            logger.error(f"خطأ في الحصول على عناصر القائمة: {e}")
            return []
    
    def get_categories(self) -> List[Dict]:
        """الحصول على الفئات"""
        try:
            self.cursor.execute('''
                SELECT * FROM categories WHERE is_active = 1 ORDER BY name_ar
            ''')
            return [dict(row) for row in self.cursor.fetchall()]
            
        except sqlite3.Error as e:
            logger.error(f"خطأ في الحصول على الفئات: {e}")
            return []
    
    def create_order(self, order_data: Dict) -> Optional[int]:
        """إنشاء طلب جديد"""
        try:
            # إنشاء رقم طلب فريد
            order_number = f"ORD{datetime.now().strftime('%Y%m%d%H%M%S')}"
            
            self.cursor.execute('''
                INSERT INTO orders (
                    order_number, table_id, customer_name, customer_phone,
                    order_type, subtotal, tax_amount, discount_amount,
                    total_amount, notes, user_id
                ) VALUES (?, ?, ?, ?, ?, ?, ?, ?, ?, ?, ?)
            ''', (
                order_number,
                order_data.get('table_id'),
                order_data.get('customer_name'),
                order_data.get('customer_phone'),
                order_data.get('order_type', 'dine_in'),
                order_data.get('subtotal', 0),
                order_data.get('tax_amount', 0),
                order_data.get('discount_amount', 0),
                order_data.get('total_amount', 0),
                order_data.get('notes'),
                order_data.get('user_id')
            ))
            
            order_id = self.cursor.lastrowid
            self.connection.commit()
            
            return order_id
            
        except sqlite3.Error as e:
            logger.error(f"خطأ في إنشاء الطلب: {e}")
            self.connection.rollback()
            return None
    
    def add_order_items(self, order_id: int, items: List[Dict]) -> bool:
        """إضافة عناصر إلى الطلب"""
        try:
            for item in items:
                self.cursor.execute('''
                    INSERT INTO order_items (
                        order_id, menu_item_id, quantity, unit_price, total_price, special_notes
                    ) VALUES (?, ?, ?, ?, ?, ?)
                ''', (
                    order_id,
                    item['menu_item_id'],
                    item['quantity'],
                    item['unit_price'],
                    item['total_price'],
                    item.get('special_notes')
                ))
            
            self.connection.commit()
            return True
            
        except sqlite3.Error as e:
            logger.error(f"خطأ في إضافة عناصر الطلب: {e}")
            self.connection.rollback()
            return False
    
    def get_available_tables(self) -> List[Dict]:
        """الحصول على الطاولات المتاحة"""
        try:
            self.cursor.execute('''
                SELECT * FROM restaurant_tables 
                WHERE is_available = 1 AND is_active = 1 
                ORDER BY table_number
            ''')
            return [dict(row) for row in self.cursor.fetchall()]
            
        except sqlite3.Error as e:
            logger.error(f"خطأ في الحصول على الطاولات: {e}")
            return []
    
    def get_daily_sales(self, date: str = None) -> Dict:
        """الحصول على مبيعات اليوم"""
        try:
            if not date:
                date = datetime.now().strftime('%Y-%m-%d')
            
            # إجمالي المبيعات
            self.cursor.execute('''
                SELECT 
                    COUNT(*) as total_orders,
                    COALESCE(SUM(total_amount), 0) as total_sales,
                    COALESCE(SUM(tax_amount), 0) as total_tax,
                    COALESCE(SUM(discount_amount), 0) as total_discount
                FROM orders 
                WHERE DATE(created_at) = ? AND status != 'cancelled'
            ''', (date,))
            
            sales_data = dict(self.cursor.fetchone())
            
            # المبيعات حسب طريقة الدفع
            self.cursor.execute('''
                SELECT 
                    p.payment_method,
                    COALESCE(SUM(p.amount), 0) as amount
                FROM payments p
                JOIN orders o ON p.order_id = o.id
                WHERE DATE(o.created_at) = ? AND p.status = 'completed'
                GROUP BY p.payment_method
            ''', (date,))
            
            payment_methods = {row['payment_method']: row['amount'] for row in self.cursor.fetchall()}
            
            sales_data.update({
                'cash_sales': payment_methods.get('cash', 0),
                'card_sales': payment_methods.get('card', 0),
                'digital_sales': payment_methods.get('digital', 0)
            })
            
            return sales_data
            
        except sqlite3.Error as e:
            logger.error(f"خطأ في الحصول على المبيعات: {e}")
            return {}
    
    def process_payment(self, order_id: int, payment_data: Dict) -> bool:
        """معالجة الدفع"""
        try:
            self.cursor.execute('''
                INSERT INTO payments (
                    order_id, payment_method, amount, received_amount, 
                    change_amount, transaction_id, status
                ) VALUES (?, ?, ?, ?, ?, ?, ?)
            ''', (
                order_id,
                payment_data.get('payment_method'),
                payment_data.get('amount'),
                payment_data.get('received_amount'),
                payment_data.get('change_amount', 0),
                payment_data.get('transaction_id'),
                'completed'
            ))
            
            # تحديث حالة الطلب
            self.cursor.execute('''
                UPDATE orders SET status = 'served', updated_at = CURRENT_TIMESTAMP 
                WHERE id = ?
            ''', (order_id,))
            
            self.connection.commit()
            return True
            
        except sqlite3.Error as e:
            logger.error(f"خطأ في معالجة الدفع: {e}")
            self.connection.rollback()
            return False
    
    def get_order_details(self, order_id: int) -> Optional[Dict]:
        """الحصول على تفاصيل الطلب"""
        try:
            # معلومات الطلب الأساسية
            self.cursor.execute('''
                SELECT o.*, u.full_name as cashier_name, rt.table_number
                FROM orders o
                LEFT JOIN users u ON o.user_id = u.id
                LEFT JOIN restaurant_tables rt ON o.table_id = rt.id
                WHERE o.id = ?
            ''', (order_id,))
            
            order = self.cursor.fetchone()
            if not order:
                return None
            
            order_data = dict(order)
            
            # عناصر الطلب
            self.cursor.execute('''
                SELECT oi.*, mi.name_ar, mi.name
                FROM order_items oi
                JOIN menu_items mi ON oi.menu_item_id = mi.id
                WHERE oi.order_id = ?
            ''', (order_id,))
            
            order_data['items'] = [dict(row) for row in self.cursor.fetchall()]
            
            # معلومات الدفع
            self.cursor.execute('''
                SELECT * FROM payments WHERE order_id = ? AND status = 'completed'
            ''', (order_id,))
            
            payments = [dict(row) for row in self.cursor.fetchall()]
            order_data['payments'] = payments
            
            return order_data
            
        except sqlite3.Error as e:
            logger.error(f"خطأ في الحصول على تفاصيل الطلب: {e}")
            return None
    
    def update_menu_item_availability(self, item_id: int, is_available: bool) -> bool:
        """تحديث توفر الصنف"""
        try:
            self.cursor.execute('''
                UPDATE menu_items SET is_available = ? WHERE id = ?
            ''', (1 if is_available else 0, item_id))
            
            self.connection.commit()
            return True
            
        except sqlite3.Error as e:
            logger.error(f"خطأ في تحديث توفر الصنف: {e}")
            return False
    
    def get_low_stock_items(self, threshold_multiplier: float = 1.0) -> List[Dict]:
        """الحصول على الأصناف منخفضة المخزون"""
        try:
            self.cursor.execute('''
                SELECT * FROM inventory 
                WHERE current_stock <= (minimum_stock * ?) AND is_active = 1
                ORDER BY (current_stock / minimum_stock) ASC
            ''', (threshold_multiplier,))
            
            return [dict(row) for row in self.cursor.fetchall()]
            
        except sqlite3.Error as e:
            logger.error(f"خطأ في الحصول على أصناف المخزون المنخفض: {e}")
            return []
    
    def update_table_status(self, table_id: int, is_available: bool) -> bool:
        """تحديث حالة الطاولة"""
        try:
            self.cursor.execute('''
                UPDATE restaurant_tables SET is_available = ? WHERE id = ?
            ''', (1 if is_available else 0, table_id))
            
            self.connection.commit()
            return True
            
        except sqlite3.Error as e:
            logger.error(f"خطأ في تحديث حالة الطاولة: {e}")
            return False
    
    def get_orders_by_status(self, status: str) -> List[Dict]:
        """الحصول على الطلبات حسب الحالة"""
        try:
            self.cursor.execute('''
                SELECT o.*, u.full_name as cashier_name, rt.table_number
                FROM orders o
                LEFT JOIN users u ON o.user_id = u.id
                LEFT JOIN restaurant_tables rt ON o.table_id = rt.id
                WHERE o.status = ?
                ORDER BY o.created_at DESC
            ''', (status,))
            
            return [dict(row) for row in self.cursor.fetchall()]
            
        except sqlite3.Error as e:
            logger.error(f"خطأ في الحصول على الطلبات: {e}")
            return []
    
    def backup_database(self, backup_path: str) -> bool:
        """إنشاء نسخة احتياطية من قاعدة البيانات"""
        try:
            import shutil
            shutil.copy2(self.db_path, backup_path)
            logger.info(f"تم إنشاء نسخة احتياطية في: {backup_path}")
            return True
            
        except Exception as e:
            logger.error(f"خطأ في إنشاء النسخة الاحتياطية: {e}")
            return False
    
    def execute_custom_query(self, query: str, params: tuple = ()) -> List[Dict]:
        """تنفيذ استعلام مخصص"""
        try:
            self.cursor.execute(query, params)
            
            if query.strip().upper().startswith('SELECT'):
                return [dict(row) for row in self.cursor.fetchall()]
            else:
                self.connection.commit()
                return [{'affected_rows': self.cursor.rowcount}]
                
        except sqlite3.Error as e:
            logger.error(f"خطأ في تنفيذ الاستعلام: {e}")
            self.connection.rollback()
            return []