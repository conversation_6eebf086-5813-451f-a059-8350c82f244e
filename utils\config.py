#!/usr/bin/env python3
# -*- coding: utf-8 -*-
"""
إعدادات النظام - نظام نقطة البيع
System Configuration for POS System
"""

import os
import json
from typing import Dict, Any

class Config:
    """كلاس إعدادات النظام"""
    
    def __init__(self, config_file: str = "config.json"):
        """تهيئة الإعدادات"""
        self.config_file = config_file
        self.settings = self._load_default_settings()
        self.load_settings()
    
    def _load_default_settings(self) -> Dict[str, Any]:
        """الإعدادات الافتراضية"""
        return {
            # إعدادات التطبيق العامة
            "app": {
                "name": "نظام نقطة البيع - شاورما الدجاج",
                "version": "1.0.0",
                "language": "ar",
                "theme": "modern",
                "auto_save": True
            },
            
            # إعدادات النافذة الرئيسية
            "window": {
                "width": 1200,
                "height": 800,
                "fullscreen": False,
                "resizable": True,
                "center_on_screen": True
            },
            
            # إعدادات قاعدة البيانات
            "database": {
                "path": "pos_database.db",
                "backup_enabled": True,
                "backup_interval": 24,  # ساعات
                "max_backups": 7
            },
            
            # إعدادات الضرائب والخصومات
            "business": {
                "tax_rate": 0.14,  # 14% ضريبة القيمة المضافة
                "currency": "ر.س",
                "currency_symbol": "﷼",
                "default_discount": 0.0,
                "max_discount": 0.50,  # 50% حد أقصى للخصم
                "service_charge": 0.0
            },
            
            # إعدادات الطباعة
            "printer": {
                "enabled": True,
                "printer_name": "default",
                "paper_width": 80,  # مم
                "auto_print": True,
                "copies": 1,
                "receipt_footer": "شكراً لزيارتكم - مطعم شاورما الدجاج"
            },
            
            # إعدادات الواجهة
            "ui": {
                "font_family": "Tahoma",
                "font_size": 12,
                "button_height": 50,
                "icon_size": 32,
                "grid_columns": 4,
                "animation_enabled": True
            },
            
            # إعدادات الأمان
            "security": {
                "session_timeout": 30,  # دقائق
                "max_login_attempts": 5,
                "password_min_length": 6,
                "auto_logout": True
            },
            
            # إعدادات التقارير
            "reports": {
                "default_period": "daily",
                "auto_backup": True,
                "export_format": "pdf",
                "include_charts": True
            },
            
            # إعدادات الإشعارات
            "notifications": {
                "sound_enabled": True,
                "popup_enabled": True,
                "low_stock_alert": True,
                "order_ready_alert": True
            },
            
            # معلومات المطعم
            "restaurant": {
                "name": "مطعم شاورما الدجاج",
                "name_en": "Shawarma Chicken Restaurant",
                "address": "شارع الملك فهد، الرياض، المملكة العربية السعودية",
                "phone": "+966 11 234 5678",
                "email": "<EMAIL>",
                "website": "www.shawarma-chicken.com",
                "logo_path": "assets/logo.png",
                "cr_number": "1234567890",
                "vat_number": "300123456789003"
            }
        }
    
    def load_settings(self):
        """تحميل الإعدادات من الملف"""
        try:
            if os.path.exists(self.config_file):
                with open(self.config_file, 'r', encoding='utf-8') as f:
                    file_settings = json.load(f)
                    self._merge_settings(file_settings)
        except Exception as e:
            print(f"خطأ في تحميل الإعدادات: {e}")
    
    def save_settings(self):
        """حفظ الإعدادات في الملف"""
        try:
            with open(self.config_file, 'w', encoding='utf-8') as f:
                json.dump(self.settings, f, ensure_ascii=False, indent=4)
            return True
        except Exception as e:
            print(f"خطأ في حفظ الإعدادات: {e}")
            return False
    
    def _merge_settings(self, new_settings: Dict[str, Any]):
        """دمج الإعدادات الجديدة مع الموجودة"""
        def merge_dict(base: Dict, new: Dict):
            for key, value in new.items():
                if key in base and isinstance(base[key], dict) and isinstance(value, dict):
                    merge_dict(base[key], value)
                else:
                    base[key] = value
        
        merge_dict(self.settings, new_settings)
    
    def get(self, key_path: str, default=None):
        """الحصول على قيمة إعداد معين"""
        keys = key_path.split('.')
        value = self.settings
        
        try:
            for key in keys:
                value = value[key]
            return value
        except (KeyError, TypeError):
            return default
    
    def set(self, key_path: str, value: Any):
        """تعيين قيمة إعداد معين"""
        keys = key_path.split('.')
        setting_dict = self.settings
        
        for key in keys[:-1]:
            if key not in setting_dict:
                setting_dict[key] = {}
            setting_dict = setting_dict[key]
        
        setting_dict[keys[-1]] = value
    
    def reset_to_defaults(self):
        """إعادة تعيين الإعدادات إلى القيم الافتراضية"""
        self.settings = self._load_default_settings()
        return self.save_settings()
    
    # خصائص سريعة للوصول للإعدادات الشائعة
    @property
    def app_name(self) -> str:
        return self.get('app.name', 'نظام نقطة البيع')
    
    @property
    def window_size(self) -> tuple:
        return (self.get('window.width', 1200), self.get('window.height', 800))
    
    @property
    def tax_rate(self) -> float:
        return self.get('business.tax_rate', 0.14)
    
    @property
    def currency(self) -> str:
        return self.get('business.currency', 'ر.س')
    
    @property
    def currency_symbol(self) -> str:
        return self.get('business.currency_symbol', '﷼')
    
    @property
    def font_family(self) -> str:
        return self.get('ui.font_family', 'Tahoma')
    
    @property
    def font_size(self) -> int:
        return self.get('ui.font_size', 12)
    
    @property
    def restaurant_name(self) -> str:
        return self.get('restaurant.name', 'مطعم شاورما الدجاج')
    
    @property
    def restaurant_phone(self) -> str:
        return self.get('restaurant.phone', '+966 11 234 5678')