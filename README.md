# نظام نقطة البيع - مطعم شاورما الدجاج
# Shawarma Chicken POS System

نظام نقطة بيع متكامل ومتطور مصمم خصيصاً لمطعم شاورما الدجاج باللغة العربية.

## المميزات الرئيسية

### 🏪 وظائف نقطة البيع
- واجهة نقطة بيع سهلة الاستخدام وسريعة الاستجابة
- إدارة الطلبات مع إمكانية التخصيص
- حساب الضرائب والخصومات تلقائياً
- دعم طرق دفع متعددة (نقدي، بطاقة، محفظة رقمية)
- طباعة الفواتير الحرارية
- طباعة طلبات المطبخ

### 👥 إدارة المستخدمين
- نظام تسجيل دخول آمن
- أدوار مستخدمين متعددة (مدير، كاشير، مدير مناوب)
- تتبع جلسات المستخدمين
- حماية بكلمة مرور مشفرة

### 📊 التقارير والإحصائيات
- تقارير مبيعات يومية ومرحلية
- إحصائيات الأصناف الأكثر مبيعاً
- تحليل طرق الدفع
- تقارير الضرائب والخصومات
- إمكانية طباعة وتصدير التقارير

### 🍽️ إدارة القائمة
- إدارة الأصناف والفئات
- تحديث الأسعار
- إضافة أوصاف ومعلومات غذائية
- إدارة توفر الأصناف

### 🏢 إدارة الطاولات
- نظام إدارة الطاولات
- تتبع حالة الطاولات (متاحة/مشغولة)
- ربط الطلبات بالطاولات

### 📦 إدارة المخزون
- تتبع مستويات المخزون
- تنبيهات المخزون المنخفض
- تقارير المخزون

## متطلبات النظام

### متطلبات أساسية
- **نظام التشغيل**: Windows 10/11، Linux، macOS
- **Python**: الإصدار 3.8 أو أحدث
- **الذاكرة**: 4 جيجابايت RAM كحد أدنى
- **التخزين**: 500 ميجابايت مساحة فارغة
- **الشاشة**: دقة 1024x768 كحد أدنى (مُوصى: 1920x1080)

### متطلبات اختيارية
- **طابعة حرارية**: لطباعة الفواتير (80mm أو 58mm)
- **ماسح باركود**: لقراءة الباركودات
- **درج نقود**: متصل بالطابعة
- **شاشة عميل**: لعرض الطلب للعميل

## التثبيت والإعداد

### 1. تحميل النظام
```bash
# استنساخ المشروع أو تحميل الملفات
git clone [repository-url]
cd shawarma-chicken-pos
```

### 2. تثبيت المتطلبات
```bash
# تثبيت المكتبات الأساسية (معظمها مدمج مع Python)
pip install -r requirements.txt

# لميزات إضافية (اختيارية)
pip install openpyxl reportlab qrcode[pil] pywin32
```

### 3. إعداد قاعدة البيانات
قاعدة البيانات ستُنشأ تلقائياً عند أول تشغيل للنظام مع البيانات الافتراضية.

### 4. تشغيل النظام
```bash
python main.py
```

## بيانات الدخول الافتراضية

### المدير
- **اسم المستخدم**: `admin`
- **كلمة المرور**: `admin123`

### الكاشير
- **اسم المستخدم**: `cashier`
- **كلمة المرور**: `cashier123`

**⚠️ تحذير**: يُرجى تغيير كلمات المرور الافتراضية فوراً بعد التثبيت!

## الاستخدام

### بدء يوم عمل جديد
1. تسجيل الدخول بحساب المدير أو الكاشير
2. مراجعة مستويات المخزون
3. التأكد من عمل الطابعة
4. بدء استقبال الطلبات

### إنشاء طلب جديد
1. اختيار نوع الطلب (في المطعم/خارجي/توصيل)
2. اختيار الطاولة (للطلبات الداخلية)
3. إضافة الأصناف من القائمة
4. تطبيق أي خصومات
5. اختيار طريقة الدفع
6. إنهاء الطلب وطباعة الفاتورة

### إدارة الأصناف
1. الذهاب إلى قائمة "القائمة"
2. اختيار "إدارة الأصناف"
3. إضافة أو تعديل أو حذف الأصناف
4. تحديث الأسعار حسب الحاجة

### إنشاء التقارير
1. الذهاب إلى قائمة "التقارير"
2. اختيار نوع التقرير المطلوب
3. تحديد الفترة الزمنية
4. عرض أو طباعة أو تصدير التقرير

## إعداد الطابعة

### للطابعات الحرارية
1. التأكد من تثبيت تعريف الطابعة
2. تحديد اسم الطابعة في إعدادات النظام
3. اختبار الطباعة من قائمة الإعدادات

### إعدادات الطباعة المُوصاة
- **عرض الورق**: 80mm أو 58mm
- **عدد النسخ**: 1 (فاتورة العميل) + 1 (المطبخ)
- **قطع الورق التلقائي**: مُفعل

## النسخ الاحتياطي

### النسخ الاحتياطي التلقائي
- يتم إنشاء نسخة احتياطية يومية تلقائياً
- النسخ محفوظة في مجلد `backups`
- الاحتفاظ بآخر 7 نسخ احتياطية

### النسخ الاحتياطي اليدوي
```bash
# نسخ ملف قاعدة البيانات
cp pos_database.db backup/pos_database_$(date +%Y%m%d).db

# نسخ ملف الإعدادات
cp config.json backup/config_$(date +%Y%m%d).json
```

## استكشاف الأخطاء

### مشاكل شائعة وحلولها

#### 1. خطأ في قاعدة البيانات
```
خطأ: database is locked
الحل: إغلاق جميع نوافذ النظام وإعادة التشغيل
```

#### 2. مشكلة في الطباعة
```
خطأ: Printer not found
الحل: التأكد من تثبيت تعريف الطابعة وتصحيح اسمها في الإعدادات
```

#### 3. بطء في الأداء
```
المشكلة: النظام بطيء
الحل: إنشاء نسخة احتياطية وحذف البيانات القديمة أو زيادة الذاكرة
```

### ملفات السجلات
- سجلات النظام: `logs/application.log`
- سجلات قاعدة البيانات: `logs/database.log`
- سجلات الأخطاء: `logs/errors.log`

## التخصيص والإعدادات

### إعدادات المطعم
يمكن تعديل إعدادات المطعم من ملف `config.json`:

```json
{
  "restaurant": {
    "name": "مطعم شاورما الدجاج",
    "address": "العنوان هنا",
    "phone": "+966 11 234 5678",
    "vat_number": "300123456789003"
  }
}
```

### إعدادات الضرائب
```json
{
  "business": {
    "tax_rate": 0.15,
    "currency": "ر.س",
    "service_charge": 0.0
  }
}
```

## الأمان والحماية

### احتياطات الأمان
- تشفير كلمات المرور
- جلسات محدودة المدة
- سجلات مراجعة شاملة
- صلاحيات مستخدمين محددة

### النصائح الأمنية
1. تغيير كلمات المرور الافتراضية
2. إنشاء نسخ احتياطية منتظمة
3. تحديث النظام دورياً
4. مراقبة سجلات النشاط

## الدعم الفني

### معلومات الاتصال
- **البريد الإلكتروني**: <EMAIL>
- **الهاتف**: +966 11 234 5678
- **ساعات الدعم**: الأحد - الخميس، 9:00 ص - 6:00 م

### التحديثات
- يتم إصدار تحديثات دورية لتحسين الأداء وإضافة ميزات جديدة
- سيتم إشعاركم بالتحديثات المتوفرة
- النسخ الاحتياطية مُوصاة قبل أي تحديث

## الترخيص

هذا النظام مُطور خصيصاً لمطعم شاورما الدجاج. جميع الحقوق محفوظة.

## المساهمة في التطوير

إذا كان لديكم اقتراحات أو تحسينات، يُرجى التواصل معنا عبر البريد الإلكتروني.

---

**© 2024 نظام نقطة البيع - مطعم شاورما الدجاج. جميع الحقوق محفوظة.**